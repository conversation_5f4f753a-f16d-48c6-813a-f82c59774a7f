import os
import io
from matplotlib.colors import rgb2hex
import os
import matplotlib.pyplot as plt
import numpy as np
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import umap
import torch
import yaml
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
from PIL import Image
from matplotlib.patches import Patch
import argparse
import os

# from models_vnca import VNCA
from models.MNCA.models_mnca_deep import DyNCA
from src.dataset_spot_token import SpotDataset

from mpl_toolkits.mplot3d import Axes3D


device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')


def plot_pca_rgb_comb(z_mean, texture_names, epoch, output_dir, name_c=3, method='umap'):
    plt.figure(figsize=(15, 15))
    
    z_mean = np.array(z_mean, dtype=np.float32)
    
    print(f"Initial data shape: {z_mean.shape}")
    print(f"Initial data range: [{z_mean.min()}, {z_mean.max()}]")
    
    valid_indices = ~np.any(np.isnan(z_mean) | np.isinf(z_mean), axis=1)
    z_mean = z_mean[valid_indices]
    texture_names = [name for i, name in enumerate(texture_names) if valid_indices[i]]
    
    scaler = StandardScaler()
    z_mean = scaler.fit_transform(z_mean)
    
    z_mean = np.clip(z_mean, -10, 10)
    
    print(f"Processed data shape: {z_mean.shape}")
    print(f"Processed data range: [{z_mean.min()}, {z_mean.max()}]")
    
    # pca_3d = PCA(ensure_all_finite=True, n_components=3)
    pca_3d = PCA(n_components=3)
    pca_features = pca_3d.fit_transform(z_mean)
    pca_rgb = (pca_features - pca_features.min(axis=0)) / (pca_features.max(axis=0) - pca_features.min(axis=0))
    
    pca_hex = [rgb2hex(pca_rgb[i,:]) for i in range(pca_rgb.shape[0])]
    # pca_hex = list(map(rgb2hex, pca_rgb))
    
    if method.lower() == 'umap':
        reducer = umap.UMAP(random_state=42)
        # reducer = umap.UMAP(n_jobs=-1)
        title = f'Latent Space UMAP with PCA RGB - All Samples (Epoch {epoch})'
    else:  # PCA
        reducer = PCA(n_components=2)
        title = f'Latent Space PCA with PCA RGB - All Samples (Epoch {epoch})'
    z_embedded = reducer.fit_transform(z_mean)
    plt.figure(figsize=(15, 15))
    
    categories = list(set(name[:name_c] for name in texture_names))
    categories.sort()
    
    category_points = []
    category_labels = []
    category_colors = []
    
    plt.scatter(z_embedded[:, 0], z_embedded[:, 1], c=pca_hex, s=100, alpha=0.8)
    
    for category in categories:
        mask = [name[:name_c] == category for name in texture_names]
        mask = np.array(mask)
        if np.any(mask):
            avg_color = np.mean(pca_rgb[mask], axis=0)
            avg_hex = rgb2hex(avg_color)
            
            category_colors.append(avg_hex)
            category_labels.append(category)
    
    
    legend_elements = [Patch(facecolor=color, edgecolor='black', label=label) 
                      for color, label in zip(category_colors, category_labels)]
    
    plt.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1.26, 1), title='Categories', 
               fontsize=12, title_fontsize=14)
    # plt.legend(handles=legend_elements, loc='upper right', title='Categories', fontsize=12, title_fontsize=14)
    
    # points label
    # for i, txt in enumerate(texture_names):
    #     plt.annotate(txt, (z_embedded[i, 0], z_embedded[i, 1]), 
    #                  fontsize=8, alpha=0.7,
    #                  xytext=(5, 5), textcoords='offset points')
    
    plt.title(title)
    plt.xlabel(f'{method.upper()} 1')
    plt.ylabel(f'{method.upper()} 2')
    plt.tight_layout(rect=[0, 0, 1, 0.95])
    plt.subplots_adjust(top=0.9, bottom=0.1, left=0.1, right=0.9) ###
    
    save_dir = os.path.join(output_dir, f'pca_rgb_comb_{method.lower()}_plots')
    os.makedirs(save_dir, exist_ok=True)
    plt.savefig(os.path.join(save_dir, f'pca_rgb_comb_{method.lower()}_epoch_{epoch}.pdf'),
                bbox_inches='tight',
                dpi=300)
    
    plt.figure(figsize=(15, 5))
    
    for i in range(3):
        plt.subplot(1, 3, i+1)
        plt.hist(pca_features[:, i], bins=30)
        plt.title(f'PCA Component {i+1}')
        plt.xlabel('Value')
        plt.ylabel('Frequency')
    
    plt.tight_layout(rect=[0, 0, 1, 0.95])
    plt.savefig(os.path.join(save_dir, f'pca_components_dist_epoch_{epoch}.png'),
                bbox_inches='tight',
                dpi=300)
    
    return pca_features, pca_rgb, pca_hex


def plot_with_pca_colors(z_mean, texture_names, pca_rgb_comb, pca_hex_comb, epoch, output_dir, name_c=3, method='umap'):

    plt.figure(figsize=(15, 15))
    
    z_mean = np.array(z_mean, dtype=np.float32)
    
    print(f"Initial data shape: {z_mean.shape}")
    print(f"Initial data range: [{z_mean.min()}, {z_mean.max()}]")
    
    valid_indices = ~np.any(np.isnan(z_mean) | np.isinf(z_mean), axis=1)
    z_mean = z_mean[valid_indices]
    texture_names = [name for i, name in enumerate(texture_names) if valid_indices[i]]
    
    pca_rgb_valid = pca_rgb_comb[valid_indices]
    pca_hex_valid = [pca_hex_comb[i] for i, valid in enumerate(valid_indices) if valid]
    
    scaler = StandardScaler()
    z_mean = scaler.fit_transform(z_mean)
    
    z_mean = np.clip(z_mean, -10, 10)
    
    print(f"Processed data shape: {z_mean.shape}")
    print(f"Processed data range: [{z_mean.min()}, {z_mean.max()}]")
    
    if method.lower() == 'umap':
        reducer = umap.UMAP(random_state=42)
        z_embedded = reducer.fit_transform(z_mean)
        title = f'Latent Space UMAP with PCA RGB - All Samples (Epoch {epoch})'
    else:  # PCA
        reducer = PCA(n_components=2)
        z_embedded = reducer.fit_transform(z_mean)
        title = f'Latent Space PCA with PCA RGB - All Samples (Epoch {epoch})'
    
    plt.figure(figsize=(15, 15))
    
    categories = list(set(name[:name_c] for name in texture_names))
    categories.sort()
    
    markers = ['o', 's', '^', 'D', 'v', '<', '>', 'p', 'h', '8', '*', 'H', '+', 'x']
    while len(markers) < len(categories):
        markers.extend(markers)
    
    for idx, category in enumerate(categories):
        mask = [name[:name_c] == category for name in texture_names]
        mask = np.array(mask)
        if np.any(mask):
            cat_embedded = z_embedded[mask]
            cat_colors = [pca_hex_valid[i] for i, m in enumerate(mask) if m]
            
            plt.scatter(
                cat_embedded[:, 0], 
                cat_embedded[:, 1], 
                c=cat_colors,  # 使用 PCA RGB 颜色
                marker=markers[idx],  # 使用不同的标记形状
                s=100, 
                alpha=0.8,
                label=f"{category}"  # 添加类别标签
            )
    
    plt.legend(loc='upper right', bbox_to_anchor=(1.26, 1),  title='Categories', 
               fontsize=12, title_fontsize=14)
    
    # points label
    # for i, txt in enumerate(texture_names):
    #     plt.annotate(txt, (z_embedded[i, 0], z_embedded[i, 1]), 
    #                  fontsize=8, alpha=0.7,
    #                  xytext=(5, 5), textcoords='offset points')
    
    plt.title(title)
    plt.xlabel(f'{method.upper()} 1')
    plt.ylabel(f'{method.upper()} 2')
    plt.tight_layout(rect=[0, 0, 1, 0.95])
    plt.subplots_adjust(top=0.9, bottom=0.1)
    
    save_dir = os.path.join(output_dir, f'pca_rgb_{method.lower()}_plots')
    os.makedirs(save_dir, exist_ok=True)
    plt.savefig(os.path.join(save_dir, f'pca_rgb_comb_{method.lower()}_epoch_{epoch}.pdf'),
                bbox_inches='tight',
                dpi=300)
    
    # 创建颜色图例
    plt.figure(figsize=(10, 10))
    ax = plt.subplot(111, projection='3d')
    
    # 为每个类别单独绘制 3D 散点图，使用不同的标记形状
    for idx, category in enumerate(categories):
        mask = [name[:name_c] == category for name in texture_names]
        mask = np.array(mask)
        if np.any(mask):
            # 获取该类别的数据点
            cat_rgb = pca_rgb_valid[mask]
            cat_colors = [pca_hex_valid[i] for i, m in enumerate(mask) if m]
            
            # 绘制该类别的 3D 散点图
            ax.scatter(
                cat_rgb[:, 0], 
                cat_rgb[:, 1], 
                cat_rgb[:, 2], 
                c=cat_colors,
                marker=markers[idx],
                s=100,
                label=f"{category}"
            )
    
    # 添加图例
    ax.legend(loc='upper right', bbox_to_anchor=(1.38, 1), title='Categories', 
              fontsize=10, title_fontsize=12)
    
    ax.set_xlabel('PCA 1 (R)')
    ax.set_ylabel('PCA 2 (G)')
    ax.set_zlabel('PCA 3 (B)')
    ax.set_title('PCA RGB Color Space')
    
    plt.tight_layout(rect=[0, 0, 1, 0.95])
    plt.savefig(os.path.join(save_dir, f'pca_rgb_color_space_epoch_{epoch}.pdf'),
                bbox_inches='tight',
                dpi=300)
    
    return z_embedded, pca_rgb_valid, pca_hex_valid


def plot_pca_rgb(text_embeddings, all_texture_names, epoch, save_dir):
    # Perform PCA to reduce to 3 dimensions
    pca = PCA(n_components=3)
    reduced_embeddings = pca.fit_transform(text_embeddings)
    # import pdb; pdb.set_trace()
    
    # Create a 3D scatter plot
    fig = plt.figure(figsize=(10, 10))
    ax = fig.add_subplot(111, projection='3d')
    
    # Scatter plot
    scatter = ax.scatter(reduced_embeddings[:, 0], reduced_embeddings[:, 1], reduced_embeddings[:, 2], c=np.arange(len(all_texture_names)), cmap='viridis', marker='o')
    
    # Add labels and title
    ax.set_xlabel('Principal Component 1')
    ax.set_ylabel('Principal Component 2')
    ax.set_zlabel('Principal Component 3')
    ax.set_title(f'PCA Visualization of Text Embeddings (Epoch {epoch})')
    
    # Add a color bar
    cbar = fig.colorbar(scatter, ax=ax, shrink=0.5, aspect=5)
    cbar.set_label('Sample Index')
    
    # Save the plot
    plot_path = f'{save_dir}/pca_visualization_epoch_{epoch}.png'
    plt.savefig(plot_path)
    plt.close(fig)

def plot_umap_rgb(text_embeddings, all_texture_names, epoch, save_dir):
    # Perform UMAP to reduce to 3 dimensions
    reducer = umap.UMAP(n_components=3)
    reduced_embeddings = reducer.fit_transform(text_embeddings)
    
    # Create a 3D scatter plot
    fig = plt.figure(figsize=(10, 10))
    ax = fig.add_subplot(111, projection='3d')
    
    # Scatter plot
    scatter = ax.scatter(reduced_embeddings[:, 0], reduced_embeddings[:, 1], reduced_embeddings[:, 2], c=np.arange(len(all_texture_names)), cmap='viridis', marker='o')
    
    # Add labels and title
    ax.set_xlabel('UMAP Component 1')
    ax.set_ylabel('UMAP Component 2')
    ax.set_zlabel('UMAP Component 3')
    ax.set_title(f'UMAP Visualization of Text Embeddings (Epoch {epoch})')
    
    # Add a color bar
    cbar = fig.colorbar(scatter, ax=ax, shrink=0.5, aspect=5)
    cbar.set_label('Sample Index')
    
    # Save the plot
    plot_path = f'{save_dir}/umap_visualization_epoch_{epoch}.png'
    plt.savefig(plot_path)
    plt.close(fig)
    

if __name__ == "__main__":
    # HF and HEST arguments
    parser = argparse.ArgumentParser()  
    parser.add_argument("--HF_sample_name", type=str, default='control_P1', help="Name of the HF sample")  # control_P7/17, FZ_GT_P4/19, FZ_P14/18/20, GT_IZ_P9_rep2, GT_IZ_P9/13/15, IZ_BZ_P2, IZ_P3/10/15/16, RZ_BZ_P2/3/12, RZ_FZ_P5, RZ_GT_P2, RZ_P3/6/9/11
    parser.add_argument("--HEST_sample_name", type=str, default='MISC13', help="Name of the HEST sample") #MISC13 INT1~24, MEND33~41/45/47/48/49/51/54/59~68/85~96/139~153/162, MISC13~73/101~142, NCBI524~526/534~540/591~595/600~603/637/641~643/653~657/675~675/681~684, TENX13/14/23/24/39~41/46/53/62/65/68/70~73/89~92/152/ZEN36~49
    parser.add_argument("--sample_size", type=int, default=100, help="Number of samples to use")
    parser.add_argument("--embed_dim", type=int, default=20, help="Dimension of the text embedding")
    parser.add_argument('--n_layers', type=int, default=2, help="Number of layers in the update network")
    args = parser.parse_args()

    config_path = 'configs/MNCA.yml'
    # vnca_weights = 'results/MNCA/mnca_varker_1000/final_model.pth'
    name_count = 20
    save_dir = 'plot_results/MNCA/mnca_text_token'
    epoch = 4000
    os.makedirs(save_dir, exist_ok=True)
    with open(config_path, 'r') as stream:
        config = yaml.load(stream, Loader=yaml.FullLoader)

    # nca = DyNCA(**config['model']['attr']).to(device)
    # state_dict = torch.load(vnca_weights, map_location=device)
    # nca.load_state_dict(state_dict, strict=False)

    # import pdb; pdb.set_trace()

    # dataloader
    dataset = SpotDataset(args.HF_sample_name, args.HEST_sample_name, args.embed_dim, sample_size=args.sample_size)
    dataloader = DataLoader(dataset, batch_size=1, shuffle=True, num_workers=4, drop_last=False)

    text_embeddings = []
    all_texture_names = []
    for idx, (_, text_embedding, texture_name) in enumerate(dataset):
        text_embeddings.append(text_embedding)
        all_texture_names.append(texture_name)
    text_embeddings = torch.stack(text_embeddings).numpy()
    all_texture_names = np.array(all_texture_names)

    
    pca_features, pca_rgb_comb, pca_hex_comb = plot_pca_rgb_comb(text_embeddings, all_texture_names, epoch, save_dir, name_count, method='pca')
    pca_features2, pca_rgb_comb2, pca_hex_comb2 = plot_pca_rgb_comb(text_embeddings, all_texture_names, epoch, save_dir, name_count, method='umap')
    plot_with_pca_colors(text_embeddings, all_texture_names, pca_rgb_comb, pca_hex_comb, epoch, save_dir, name_count, method='pca')
    plot_with_pca_colors(text_embeddings, all_texture_names, pca_rgb_comb2, pca_hex_comb2, epoch, save_dir, name_count, method='umap')


# python3 -m plot_results.mnca_plot_pca_rgb_token

