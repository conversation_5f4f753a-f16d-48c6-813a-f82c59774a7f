import os
import json
import torch
import numpy as np
from tqdm import tqdm

class NoiseNCA(torch.nn.Module):
    def __init__(self, chn=16, fc_dim=64, noise_level=0.1, device='cpu'):
        super().__init__()
        self.chn = chn
        self.fc_dim = fc_dim
        self.device = device
        
        self.w1 = torch.nn.Conv2d(chn*4, fc_dim, 1, bias=True, device=device)
        self.w2 = torch.nn.Conv2d(fc_dim, chn, 1, bias=False, device=device)
        torch.nn.init.xavier_normal_(self.w1.weight, gain=0.2)
        torch.nn.init.zeros_(self.w2.weight)
        
        ident = torch.tensor([[0.0,0.0,0.0],[0.0,1.0,0.0],[0.0,0.0,0.0]], device=device)
        sobel_x = torch.tensor([[-1.0,0.0,1.0],[-2.0,0.0,2.0],[-1.0,0.0,1.0]], device=device)
        lap_x = torch.tensor([[0.5,0.0,0.5],[2.0,-6.0,2.0],[0.5,0.0,0.5]], device=device)
        self.register_buffer('filters', torch.stack([ident, sobel_x, sobel_x.T, lap_x, lap_x.T]))
        
        self.register_buffer('noise_level', torch.tensor([noise_level], device=device))
    
    def forward(self, x):
        z = self.perception(x)
        z = torch.relu(self.w1(z))
        return self.w2(z)
    
    def perception(self, x):
        b, ch, h, w = x.shape
        x = x.reshape(b*ch, 1, h, w)
        x = torch.nn.functional.pad(x, [1,1,1,1], mode='circular')
        x = torch.nn.functional.conv2d(x, self.filters[:, None])
        x = x.reshape(b, -1, h, w)
        return merge_lap(x)

def merge_lap(z):
    b, c, h, w = z.shape
    z = torch.stack([z[:,::5], z[:,1::5], z[:,2::5], z[:,3::5]+z[:,4::5]], dim=2)
    return z.reshape(b, -1, h, w)


@torch.no_grad()
def torch_model_to_np(nca_model):
    """Convert NoiseNCA model to numpy arrays"""
    params = list(nca_model.parameters())
    layers = []
    
    w1_weight = params[0][:,:,0,0].detach().cpu().numpy()  # [fc_dim, chn*4]
    w1_bias = params[1].detach().cpu().numpy()             # [fc_dim]
    layer1 = np.concatenate([w1_weight, w1_bias[:,None]], axis=1).T  # [chn*4+1, fc_dim]
    layers.append(layer1[None,...])  # 添加batch维度
    
    w2_weight = params[2][:,:,0,0].detach().cpu().numpy().T  # [chn, fc_dim]
    layers.append(w2_weight[None,...])

    ident = torch.tensor([[0.0, 0.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 0.0]])
    sobel_x = torch.tensor([[-1.0, 0.0, 1.0], [-2.0, 0.0, 2.0], [-1.0, 0.0, 1.0]])
    lap_x = torch.tensor([[0.5, 0.0, 0.5], [2.0, -6.0, 2.0], [0.5, 0.0, 0.5]])

    filters = torch.stack([ident, sobel_x, sobel_x.T, lap_x, lap_x.T]).numpy()
    # filters = nca_model.filters.detach().cpu().numpy()  # [5,3,3]
    layers.append(filters[None,...])
    
    return layers

def export_noisenca_to_json(model_path, output_path):
    """Convert NoiseNCA .pth file to JSON"""
    model = NoiseNCA(chn=12, fc_dim=96)
    state_dict = torch.load(model_path, map_location='cpu')
    model.load_state_dict(state_dict)
    model.eval()
    
    np_params = torch_model_to_np(model)
    
    metadata = {
        'model_name': 'NoiseNCA',
        'chn': model.chn,
        'fc_dim': model.fc_dim,
        'noise_level': model.noise_level.item(),
        'has_pos_emb': False
    }
    
    models_js = {
        'metadata': metadata,
        'layers': []
    }
    
    for i, layer in enumerate(np_params):
        layer = np.array(layer)
        s = layer.shape
        
        # 填充为4的倍数 (WebGL要求)
        if i < 2:  # 只对权重层填充
            layer = np.pad(layer, ((0,0),(0,0),(0,(4-s[2]%4)%4)), 'constant')
            layer = layer.reshape(s[0], s[1], -1, 4)
        
        scale = float(layer.max() - layer.min())
        center = float(-layer.min() / scale)
        layer_norm = (layer - layer.min()) / scale
        
        layer_uint8 = np.round(layer_norm * 255).astype(np.uint8)
        
        models_js['layers'].append({
            'scale': scale,
            'center': center,
            'data': layer_uint8.flatten().tolist(),
            'shape': layer.shape,
            'original_shape': s
        })
    
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    with open(output_path, 'w') as f:
        json.dump(models_js, f, indent=2)
        
        
if __name__ == "__main__":
    input_path = "/home/<USER>/wanglab/tmhqxl20/NoiseNCA-main/results_new/NoiseNCA/LYM-TCGA-AAWGSCHH/weights.pt"
    output_path = "/home/<USER>/wanglab/tmhqxl20/NoiseNCA-main/model_jsons/LYM-TCGA-AAWGSCHH.json"
    
    export_noisenca_to_json(input_path, output_path)