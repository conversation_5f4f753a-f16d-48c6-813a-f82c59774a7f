import os
import argparse
import yaml
from tqdm import tqdm
import torch
from utils import *
import numpy as np
from PIL import Image

from models_nca import NoiseNCA, PENCA

from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
import torch.multiprocessing as mp
from utils.loss import TextureLoss

parser = argparse.ArgumentParser()
parser.add_argument('--config', type=str, default='configs/Noise-NCA.yml', help="Path to the config file")
parser.add_argument('--data_dir', type=str, default='data_test_two', help="Texture images directory")
parser.add_argument('--save_dir', type=str, default='results/NCA/nca_batch_external', help="Texture images directory")
parser.add_argument('--data_batch', type=int, default=1, help="Number of images per batch")


class TextureDataset(Dataset):
    def __init__(self, data_dir, max_size=128):
        self.image_paths = [os.path.join(data_dir, f) for f in os.listdir(data_dir) 
                          if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        self.transform = transforms.Compose([
            transforms.Resize(max_size),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
        ])
        
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        img_path = self.image_paths[idx]
        img = Image.open(img_path).convert('RGB')
        img = self.transform(img)
        texture_name = os.path.splitext(os.path.basename(img_path))[0]
        return img, texture_name

def main(config):

    device = torch.device(config['device'])
    config['loss']['attr']['device'] = device
    config['model']['attr']['device'] = device
    loss_fn = TextureLoss(**config['loss']['attr']).to(device)

    # dataloader
    dataset = TextureDataset(config['data_dir'])
    dataloader = DataLoader(dataset, batch_size=config['training']['batch_size'],
                          shuffle=True, num_workers=4, drop_last=False)

    nca = NoiseNCA(**config['model']['attr']).to(device)

    opt = torch.optim.Adam(nca.parameters(), config['training']['lr'])

    if 'type' not in config['training']['scheduler'] or config['training']['scheduler']['type'] == 'MultiStep':
        lr_sched = torch.optim.lr_scheduler.MultiStepLR(opt, **config['training']['scheduler']['attr'])
    elif config['training']['scheduler']['type'] == 'Cyclic':
        lr_sched = torch.optim.lr_scheduler.CyclicLR(opt, **config['training']['scheduler']['attr'])

    iterations = config['training']['iterations']
    alpha = config['training']['overflow_weight']
    step_range = config['training']['nca']['step_range']
    inject_seed_step = config['training']['nca']['inject_seed_step']
    pool_size = config['training']['nca']['pool_size']

    # output dir
    texture_names = [os.path.splitext(f)[0] for f in os.listdir(config['data_dir'])
                   if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
    output_dirs = {}
    for name in texture_names:
        output_dir = os.path.join(config['experiment_path'],  name)
        os.makedirs(output_dir, exist_ok=True)
        output_dirs[name] = output_dir
    
    pools = {}
    for idx, (_, texture_name) in enumerate(dataset):
        pools[texture_name] = (torch.rand(pool_size, config['model']['attr']['chn'], 128, 128, device=device) - 0.5) * 0.25

    for epoch in tqdm(range(iterations)):
        for idx, (target_images, texture_names) in enumerate(dataloader):
            target_images = target_images.to(device)
            current_batch_size = target_images.size(0)
            
            batch_x = []
            batch_indices = []
            
            for i, texture_name in enumerate(texture_names):
                pool = pools[texture_name]
                pool_idx = np.random.choice(pool_size, 1, replace=False) 
                x = pool[pool_idx] 
                
                if epoch % inject_seed_step == 0:
                    x = (torch.rand_like(x) - 0.5) * 0.25
                
                batch_x.append(x)
                batch_indices.append((texture_name, pool_idx))
            
            x = torch.cat(batch_x, dim=0)
            
            step_n = np.random.randint(step_range[0], step_range[1])
            for _ in range(step_n):
                x = nca(x)

            overflow_loss = (x - x.clamp(-1.0, 1.0)).abs().sum()
            texture_loss, texture_loss_per_img = loss_fn(target_images, nca.to_rgb(x))
            loss = texture_loss + alpha * overflow_loss
            print(f'epoch: {epoch}, loss: {texture_loss.item():.4f}, overflow: {overflow_loss.item():.4f}')

            opt.zero_grad()
            loss.backward()
            for p in nca.parameters():
                p.grad /= (p.grad.norm() + 1e-8)
            opt.step()
            lr_sched.step()
            
            with torch.no_grad():
                for i, (texture_name, pool_idx) in enumerate(batch_indices):
                    pools[texture_name][pool_idx] = x[i:i+1].detach()


        if (epoch % config['training']['log_interval'] == 0) or (epoch == iterations - 1):
            original_imgs = ((target_images.permute(0, 2, 3, 1).detach().cpu().numpy()/2+0.5) * 255.0).astype(np.uint8)
            for idx, (texture_name, original_img) in enumerate(zip( texture_names, original_imgs)):
                generated_states = pools[texture_name][:4]
                imgs = nca.to_rgb(generated_states).permute([0, 2, 3, 1]).detach().cpu().numpy()
                imgs = (np.clip(imgs, 0, 1) * 255.0).astype(np.uint8)
                imgs = np.hstack([original_img] + [imgs[i] for i in range(4)])
                Image.fromarray(imgs).save(f'{output_dirs[texture_name]}/{texture_name}-epoch-{epoch}.png')

    torch.save(nca.state_dict(), os.path.join(config['experiment_path'], "final_model.pth"))


if __name__ == "__main__":
    args = parser.parse_args()
    with open(args.config, 'r') as stream:
        config = yaml.load(stream, Loader=yaml.FullLoader)

    config['training']['lr'] = 0.001
    config['data_dir'] = args.data_dir
    exp_name = config['experiment_name']
    exp_path = args.save_dir
    config['experiment_path'] = exp_path
    if not os.path.exists(exp_path):
        os.makedirs(exp_path)
    main(config)

# python3 run_nca_external.py
