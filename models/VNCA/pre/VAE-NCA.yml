experiment_name: "VNCA"
description: "Training VNCA model on batch images."
device: "cuda:0"

loss:
  attr:
    loss_type: "OT"

model:
  type: "VNCA"
  noise_levels: { default: 0.25 }
  attr:
    chn: 12
    fc_dim: 480
    z_size: 16384 # 128*128
    min_steps: 8
    max_steps: 24
    update_prob: 0.5

training:
  device: "cuda:0"
  lr: 1e-4
  batch_size: 8
  iterations: 4000
  overflow_weight: 10.0
  log_interval: 100

  scheduler:
    type: "MultiStep"
    attr:
      milestones: [ 1000, 2000 ]
      gamma: 0.5

  nca:
    pool_size: 12
    step_range: [ 32, 128 ]
    inject_seed_step: 8
