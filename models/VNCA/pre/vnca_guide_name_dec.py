import os
import shutil
import argparse
import yaml
from tqdm import tqdm
import torch
import utils.utils as utils
import numpy as np
import wandb
from PIL import Image
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
import matplotlib.pyplot as plt

import umap
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from transformers import BertModel, BertTokenizer
import torch.nn as nn 

from utils.loss import TextureLoss
from models_vnca_guide_name import  VNCA

parser = argparse.ArgumentParser()
parser.add_argument('--data_dir', type=str, default='data/images_50_model_nine', help="Texture images directory")
parser.add_argument('--data_save_dir', type=str, default='results/VNCA/vnca_guide_name_dec_50_nine_delklmae', help="Saved images directory")

parser.add_argument('--config', type=str, default='configs/VNCA.yml', help="Path to the config file")
parser.add_argument('--batch_size', type=int, default=16, help="Number of images per batch")
parser.add_argument('--latent_dim', type=int, default=12, help="Number of latent channels")
parser.add_argument('--guide_dim', type=int, default=4, help="Number of guide channels")


# CUDA_VISIBLE_DEVICES='1' python3 vnca_guide_name_dec.py


def L1_dist(x, y):
    # x : shape [batch, dim]
    # y : shape [num_classes, dim]
    # dist : [batch, num_classes]
    dist = torch.abs(x[:, None, :] - y).mean()    
    return dist

def L2_dist(x, y):
    # x : shape [batch, dim]
    # y : shape [num_classes, dim]
    # dist : [batch, num_classes]
    dist = torch.sqrt(torch.sum(torch.square(x[:, None, :] - y), dim=-1)).mean()
    return dist

class TextureDataset(Dataset):
    def __init__(self, data_dir, max_size=128):
        self.image_paths = [os.path.join(data_dir, f) for f in os.listdir(data_dir)
                          if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        self.transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize(max_size),
            transforms.ToTensor(), # C H W, i.e., permute(2, 0, 1)
            transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
        ])

        texture_names = [os.path.splitext(os.path.basename(f))[0] for f in self.image_paths]
        self.name_vectors = self._generate_bert_embeddings(texture_names)

    def _generate_bert_embeddings(self, texture_names):
        # Load pre-trained BERT model and tokenizer
        tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')
        model = BertModel.from_pretrained('bert-base-uncased')
        model.eval()  
        projection = nn.Linear(768, args.guide_dim)
        nn.init.orthogonal_(projection.weight)
        projection.requires_grad_(False)            
        
        name_vectors = {}
        for name in texture_names:
            tokens = name.split('-')
            with torch.no_grad():  # Disable gradient calculation
                token_embeddings = []
                for token in tokens:
                    inputs = tokenizer(token, return_tensors='pt', padding=True, truncation=True)
                    outputs = model(**inputs)
                    token_embedding = outputs.last_hidden_state.mean(dim=1).squeeze()
                    token_embeddings.append(token_embedding)
                
                vector = torch.stack(token_embeddings).mean(dim=0)
                vector = projection(vector)
            name_vectors[name] = vector.to(dtype=torch.float32)
        
        return name_vectors

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        img = utils.imread(self.image_paths[idx], max_size=None)  # Resize is handled in transform
        img = self.transform(img)
        texture_name = os.path.splitext(os.path.basename(self.image_paths[idx]))[0]
        random_channels = (torch.rand(args.latent_dim, 128, 128) - 0.5) * 0.25

        name_embedding = self.name_vectors[texture_name].unsqueeze(1).unsqueeze(2).expand(args.guide_dim, 128, 128)
        img = torch.cat([img, random_channels, name_embedding], dim=0)

        return img, texture_name


def main(config):

    device = torch.device(config['device'])
    config['loss']['attr']['device'] = device
    config['model']['attr']['device'] = device
    loss_fn = TextureLoss(**config['loss']['attr']).to(device)

    # dataloader
    dataset = TextureDataset(config['data_dir'])
    dataloader = DataLoader(dataset, batch_size=config['training']['batch_size'],
                          shuffle=True, num_workers=4, drop_last=False)

    nca = VNCA(**config['model']['attr']).to(device)
    opt = torch.optim.Adam(nca.parameters(), config['training']['lr'])

    if 'type' not in config['training']['scheduler'] or config['training']['scheduler']['type'] == 'MultiStep':
        lr_sched = torch.optim.lr_scheduler.MultiStepLR(opt, **config['training']['scheduler']['attr'])
    elif config['training']['scheduler']['type'] == 'Cyclic':
        lr_sched = torch.optim.lr_scheduler.CyclicLR(opt, **config['training']['scheduler']['attr'])

    iterations = config['training']['iterations']
    alpha = config['training']['overflow_weight']
    step_range = config['training']['nca']['step_range']
    inject_seed_step = config['training']['nca']['inject_seed_step']
    pool_size = config['training']['nca']['pool_size']
    batch_size = config['training']['batch_size']

    # output dir
    texture_names = [os.path.splitext(f)[0] for f in os.listdir(config['data_dir'])
                   if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
    output_dirs = {}
    for name in texture_names:
        output_dir = os.path.join(config['experiment_path'], name)
        os.makedirs(output_dir, exist_ok=True)
        output_dirs[name] = output_dir


    for epoch in tqdm(range(iterations)):
        for batch_idx, (targets, texture_names) in enumerate(dataloader):
            target_process = targets.to(device)
            target_images = targets[..., :3, :, :].to(device)

            if epoch % inject_seed_step == 0:
                # target_images[:1] = (torch.rand_like(target_images[:1]) - 0.5) * 0.25
                target_process[:1][..., :3+args.latent_dim, :, :] = nca.seed(1)

            name_embeddings = target_process[..., -args.guide_dim:, :, :]
            generates, q_z_given_x, states = nca(target_process[..., :3+args.latent_dim, :, :], name_embeddings)

            # Calculate losses
            overflow_loss = (generates - generates.clamp(-1.0, 1.0)).abs().sum()
            generate_images = nca.to_rgb(generates)
            # import pdb; pdb.set_trace()
            texture_loss, _ = loss_fn(target_images, generate_images)
            # mse_loss = L2_dist(generate_images, target_images)
            mse_loss = L1_dist(generate_images, target_images)
            kl_loss = torch.distributions.kl_divergence(q_z_given_x, nca.p_z).sum(dim=1).mean()
            loss = overflow_loss + texture_loss # + mse_loss #  + kl_loss

            print('epoch: ', epoch, ' batch: ', batch_idx,
                    'texture_loss: ', texture_loss.item(),
                    'overflow_loss: ', overflow_loss.item(),
                    'kl_loss: ', kl_loss.item(),
                    'mse_loss: ', mse_loss.item())
    
            opt.zero_grad()
            loss.backward()
            for p in nca.parameters():
                if p.grad is not None:  # Check if gradient exists
                    p.grad /= (p.grad.norm() + 1e-8)

            opt.step()
            lr_sched.step()


            if (epoch % config['training']['log_interval'] == 0) or (epoch == iterations - 1):
                with torch.no_grad():
                    generated_state, generated_states = nca.generate(n_samples=config['training']['batch_size'])
                    # Use the final state for visualization
                    outputs = (nca.to_rgb(generated_state)).permute([0, 2, 3, 1]).detach().cpu().numpy()
                outputs = (np.clip(outputs, 0, 1) * 255.0).astype(np.uint8)

                with torch.no_grad():
                    name_embeddings = targets[..., -args.guide_dim:, :, :]
                    new, q_z_given_x, states = nca(targets[..., :3+args.latent_dim, :, :].to(device), name_embeddings.to(device))
                generates = (nca.to_rgb(new)).permute([0, 2, 3, 1]).detach().cpu().numpy()
                generates = (np.clip(generates, 0, 1) * 255.0).astype(np.uint8)

                original_imgs = ((targets[..., :3, :, :].permute(0, 2, 3, 1).detach().cpu().numpy()/2+0.5) * 255.0).astype(np.uint8)
                for idx, (output, original_img, generate_image, texture_name) in enumerate(zip(outputs, original_imgs, generates, texture_names)):
                    imgs = np.hstack([original_img] + [generate_image] + [output])
                    Image.fromarray(imgs).save(f'{output_dirs[texture_name]}/{texture_name}-epoch-{epoch}.png')

                # Plot latent space projections
                all_z_means = []
                all_texture_names = []
                with torch.no_grad():
                    for batch_idx, (targets, texture_names) in enumerate(dataloader):
                        target_process = targets[..., :3+args.latent_dim, :, :].to(device)
                        q_z_given_x = nca.encode(target_process)
                        z_mean = q_z_given_x.loc.detach().cpu().numpy()
                        all_z_means.append(z_mean)
                        all_texture_names.extend(texture_names)
                all_z_means = np.concatenate(all_z_means, axis=0)

                utils.plot_latent_projections(all_z_means, all_texture_names, epoch, 
                                    config['experiment_path'], method='umap')
                utils.plot_latent_projections(all_z_means, all_texture_names, epoch, 
                                    config['experiment_path'], method='pca')
            
    torch.save(nca.state_dict(), os.path.join(config['experiment_path'], "final_model.pth"))


if __name__ == "__main__":
    args = parser.parse_args()
    with open(args.config, 'r') as stream:
        config = yaml.load(stream, Loader=yaml.FullLoader)

    config['training']['lr'] = 0.001
    config['data_dir'] = args.data_dir
    config['model']['attr']['chn'] = 3 + args.latent_dim
    config['training']['batch_size'] = args.batch_size
    # exp_name = config['experiment_name']
    # exp_path = f'results/{exp_name}/vnca_each/LYM'
    exp_path = args.data_save_dir
    config['experiment_path'] = exp_path
    if not os.path.exists(exp_path):
        os.makedirs(exp_path)

    main(config)

# python3 train.py