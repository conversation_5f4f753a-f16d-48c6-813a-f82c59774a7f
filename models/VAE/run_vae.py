__author__ = 'SherlockLiao'

import torch
import torchvision
from torch import nn
from torch import optim
import torch.nn.functional as F
from torch.autograd import Variable
from torch.utils.data import DataLoader
from torchvision import transforms
from torchvision.utils import save_image
from torchvision.datasets import MNIST
import os
import argparse
from torch.utils.data import Dataset, DataLoader
import utils.utils as utils
import numpy as np
from PIL import Image

if not os.path.exists('./vae_img'):
    os.mkdir('./vae_img')


parser = argparse.ArgumentParser()
parser.add_argument('--data_dir', type=str, default='data/images_200_model_two', help="Texture images directory")
parser.add_argument('--data_save_dir', type=str, default='results/VAE/vae_one', help="Saved images directory")
# parser.add_argument('--data_dir', type=str, default='data/images_model_one', help="Texture images directory")
# parser.add_argument('--data_save_dir', type=str, default='results/VAE/vae_one', help="Saved images directory")
# parser.add_argument('--data_dir', type=str, default='data/images_model_nine/ADI', help="Texture images directory")
# parser.add_argument('--data_save_dir', type=str, default='results/VAE/vae_each/ADI', help="Saved images directory")
# parser.add_argument('--data_dir', type=str, default='data/images_model_nine/LYM', help="Texture images directory")
# parser.add_argument('--data_save_dir', type=str, default='results/VAE/vae_each/LYM', help="Saved images directory")
parser.add_argument('--batch_size', type=int, default=16, help="Number of images per batch")
parser.add_argument('--num_epochs', type=int, default=500, help="Number of epochs")
parser.add_argument('--save_interval', type=int, default=100, help="Number of epochs")
args = parser.parse_args()



class TextureDataset(Dataset):
    def __init__(self, data_dir, max_size=128):
        self.image_paths = [os.path.join(data_dir, f) for f in os.listdir(data_dir)
                          if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        self.transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize(max_size),
            transforms.ToTensor(), # C H W, i.e., permute(2, 0, 1)
            transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
        ])

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        img = utils.imread(self.image_paths[idx], max_size=None)  # Resize is handled in transform
        img = self.transform(img)
        texture_name = os.path.splitext(os.path.basename(self.image_paths[idx]))[0]
        return img, texture_name
    


class VAE(nn.Module):
    def __init__(self):
        super(VAE, self).__init__()
        self.input_dim = 3 * 128 * 128 

        self.fc1 = nn.Linear(self.input_dim, 400)
        self.fc21 = nn.Linear(400, 20)
        self.fc22 = nn.Linear(400, 20)
        self.fc3 = nn.Linear(20, 400)
        self.fc4 = nn.Linear(400, self.input_dim)

    def encode(self, x):
        x = x.view(x.size(0), -1)
        h1 = F.relu(self.fc1(x))
        return self.fc21(h1), self.fc22(h1)

    def reparametrize(self, mu, logvar):
        std = logvar.mul(0.5).exp_()
        if torch.cuda.is_available():
            eps = torch.cuda.FloatTensor(std.size()).normal_()
        else:
            eps = torch.FloatTensor(std.size()).normal_()
        eps = Variable(eps)
        return eps.mul(std).add_(mu)

    def decode(self, z):
        h3 = F.relu(self.fc3(z))
        return F.sigmoid(self.fc4(h3))

    def forward(self, x):
        mu, logvar = self.encode(x)
        z = self.reparametrize(mu, logvar)
        x = self.decode(z)
        return x, mu, logvar


texture_names = [os.path.splitext(f)[0] for f in os.listdir(args.data_dir)
                   if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
output_dirs = {}
for name in texture_names:
    output_dir = os.path.join(args.data_save_dir, name)
    os.makedirs(output_dir, exist_ok=True)
    output_dirs[name] = output_dir

dataset = TextureDataset(args.data_dir)
dataloader = DataLoader(dataset, batch_size=args.batch_size,
                          shuffle=True, num_workers=4, drop_last=False)
model = VAE()
if torch.cuda.is_available():
    model.cuda()

reconstruction_function = nn.MSELoss(size_average=False)


def loss_function(recon_x, x, mu, logvar):
    """
    recon_x: generating images
    x: origin images
    mu: latent mean
    logvar: latent log variance
    """
    # import pdb; pdb.set_trace()
    BCE = reconstruction_function(recon_x, x)  # mse loss
    # loss = 0.5 * sum(1 + log(sigma^2) - mu^2 - sigma^2)
    KLD_element = mu.pow(2).add_(logvar.exp()).mul_(-1).add_(1).add_(logvar)
    KLD = torch.sum(KLD_element).mul_(-0.5)
    print('BCE: {}, KLD: {}'.format(BCE, KLD))
    # KL divergence
    return BCE + KLD


optimizer = optim.Adam(model.parameters(), lr=1e-3)

for epoch in range(args.num_epochs):
    model.train()
    train_loss = 0
    for batch_idx, (data, data_name) in enumerate(dataloader):
        img = data
        img = img.view(img.size(0), -1)
        img = Variable(img)
        if torch.cuda.is_available():
            img = img.cuda()
        optimizer.zero_grad()
        recon_batch, mu, logvar = model(img)
        loss = loss_function(recon_batch, img, mu, logvar)
        loss.backward()
        train_loss += loss.item()
        optimizer.step()
        if batch_idx % 100 == 0:
            print('Train Epoch: {} [{}/{} ({:.0f}%)]\tLoss: {:.6f}'.format(
                epoch,
                batch_idx * len(img),
                len(dataloader.dataset), 100. * batch_idx / len(dataloader),
                loss.item() / len(img)))
    
        if (epoch % args.save_interval == 0) or (epoch == args.num_epochs - 1):
            # original_imgs = ((data.permute(0, 2, 3, 1).detach().cpu().numpy()/2+0.5) * 255.0)
            original_imgs = ((data.reshape(-1, 3, 128, 128).permute(0, 2, 3, 1).detach().cpu().numpy()/2+0.5) * 255.0)

            for idx, (recon_img, original_img, texture_name) in enumerate(zip(recon_batch, original_imgs, data_name)):
                recon_img = (recon_img.reshape(3, 128, 128).permute(1, 2, 0).detach().cpu().numpy()/2+0.5)
                recon_img = (np.clip(recon_img, 0, 1) * 255.0)
                # import pdb; pdb.set_trace()
                imgs = np.hstack([original_img] + [recon_img]).astype(np.uint8)
                Image.fromarray(imgs).save(f'{output_dirs[texture_name]}/{texture_name}-epoch-{epoch}.png')

            # Plot latent space projections
            all_z_means = []
            all_texture_names = []
            with torch.no_grad():
                for batch_idx, (targets, texture_names) in enumerate(dataloader):
                    target_process = targets.cuda()
                    mu, logvar = model.encode(target_process)
                    q_z_given_x = model.reparametrize(mu, logvar)
                    z_mean = q_z_given_x.detach().cpu().numpy()
                    all_z_means.append(z_mean)
                    all_texture_names.extend(texture_names)
            all_z_means = np.concatenate(all_z_means, axis=0)

            utils.plot_latent_projections(all_z_means, all_texture_names, epoch, 
                                args.data_save_dir, method='umap')
            utils.plot_latent_projections(all_z_means, all_texture_names, epoch, 
                                args.data_save_dir, method='pca')

    print('====> Epoch: {} Average loss: {:.4f}'.format(
        epoch, train_loss / len(dataloader.dataset)))
    # if epoch % 10 == 0:
    #     save = to_img(recon_batch.cpu().data)
    #     save_image(save, f'{output_dirs[texture_name]}/{texture_name}-epoch-{epoch}.png')

# torch.save(model.state_dict(), './vae.pth')
torch.save(model.state_dict(), os.path.join(args.data_save_dir, "vae.pth"))