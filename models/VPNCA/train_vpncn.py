import os
import shutil
import argparse
import yaml
from tqdm import tqdm
import torch
import utils.utils as utils
import numpy as np
import wandb
from PIL import Image
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
import matplotlib.pyplot as plt

from utils.loss import TextureLoss
from models_vpnca import  VNCA, NCA, NoiseNCA, PENCA

parser = argparse.ArgumentParser()
# parser.add_argument('--data_dir', type=str, default='data/images_model_nine/ADI', help="Texture images directory")
# parser.add_argument('--data_save_dir', type=str, default='results/VPNCA/vpnca_each/ADI', help="Saved images directory")
# parser.add_argument('--data_dir', type=str, default='data/images_model_nine/LYM', help="Texture images directory")
# parser.add_argument('--data_save_dir', type=str, default='results/VPNCA/vpnca_each/LYM', help="Saved images directory")
parser.add_argument('--data_dir', type=str, default='data/images_model_two', help="Texture images directory")
parser.add_argument('--data_save_dir', type=str, default='results/VPNCA/vpnca_two', help="Saved images directory")
parser.add_argument('--config', type=str, default='configs/VAE-NCA.yml', help="Path to the config file")
parser.add_argument('--batch_size', type=int, default=8, help="Number of images per batch")

# python3 train_vpncn.py

def L1_dist(x, y):
    # x : shape [batch, dim]
    # y : shape [num_classes, dim]
    # dist : [batch, num_classes]
    dist = torch.abs(x[:, None, :] - y).mean()    
    return dist

def L2_dist(x, y):
    # x : shape [batch, dim]
    # y : shape [num_classes, dim]
    # dist : [batch, num_classes]
    dist = torch.sqrt(torch.sum(torch.square(x[:, None, :] - y), dim=-1)).mean()
    return dist

class TextureDataset(Dataset):
    def __init__(self, data_dir, max_size=128):
        self.image_paths = [os.path.join(data_dir, f) for f in os.listdir(data_dir)
                          if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        self.transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize(max_size),
            transforms.ToTensor(), # C H W, i.e., permute(2, 0, 1)
            transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
        ])

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        img = utils.imread(self.image_paths[idx], max_size=None)  # Resize is handled in transform
        img = self.transform(img)
        texture_name = os.path.splitext(os.path.basename(self.image_paths[idx]))[0]
        return img, texture_name

def get_nca_model(config, texture_name=None):
    model_type = config['model']['type']
    if model_type == 'NCA':
        return NCA(**config['model']['attr'])
    elif model_type == 'NoiseNCA':
        noise_levels = config['model']['noise_levels']
        if texture_name in noise_levels:
            noise_level = noise_levels[texture_name]
        else:
            noise_level = noise_levels['default']
        return NoiseNCA(noise_level=noise_level, **config['model']['attr'])
    elif model_type == 'PENCA':
        return PENCA(**config['model']['attr'])
    elif model_type == 'VNCA':
        return VNCA(**config['model']['attr'])
    else:
        raise ValueError(f"Unknown model type: {model_type}")

def main(config):

    device = torch.device(config['device'])
    config['loss']['attr']['device'] = device
    config['model']['attr']['device'] = device
    loss_fn = TextureLoss(**config['loss']['attr']).to(device)

    # dataloader
    dataset = TextureDataset(config['data_dir'])
    dataloader = DataLoader(dataset, batch_size=config['training']['batch_size'],
                          shuffle=True, num_workers=4, drop_last=False)

    nca = get_nca_model(config, texture_name='ADI').to(device)
    opt = torch.optim.Adam(nca.parameters(), config['training']['lr'])

    if 'type' not in config['training']['scheduler'] or config['training']['scheduler']['type'] == 'MultiStep':
        lr_sched = torch.optim.lr_scheduler.MultiStepLR(opt, **config['training']['scheduler']['attr'])
    elif config['training']['scheduler']['type'] == 'Cyclic':
        lr_sched = torch.optim.lr_scheduler.CyclicLR(opt, **config['training']['scheduler']['attr'])

    iterations = config['training']['iterations']
    alpha = config['training']['overflow_weight']
    step_range = config['training']['nca']['step_range']
    inject_seed_step = config['training']['nca']['inject_seed_step']
    pool_size = config['training']['nca']['pool_size']
    batch_size = config['training']['batch_size']

    total_samples = len(dataset)
    with torch.no_grad():
        pool = nca.seed(total_samples).to(device)

    # output dir
    texture_names = [os.path.splitext(f)[0] for f in os.listdir(config['data_dir'])
                   if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
    output_dirs = {}
    for name in texture_names:
        output_dir = os.path.join(config['experiment_path'], name)
        os.makedirs(output_dir, exist_ok=True)
        output_dirs[name] = output_dir

    pools = {}
    for idx, (_, texture_name) in enumerate(dataset):
        pools[texture_name] = (torch.rand(pool_size, config['model']['attr']['chn'], 128, 128, device=device) - 0.5) * 0.25


    for epoch in tqdm(range(iterations)):
        for batch_idx, (target_images, texture_names) in enumerate(dataloader):
            target_images = target_images.to(device)

            batch_x = []
            batch_indices = []

            for i, texture_name in enumerate(texture_names):
                pool = pools[texture_name]
                pool_idx = np.random.choice(pool_size, 1, replace=False) 
                x = pool[pool_idx] 
                
                if epoch % inject_seed_step == 0:
                    # x = (torch.rand_like(x) - 0.5) * 0.25
                    x[:1] = nca.seed(1)
                
                batch_x.append(x)
                batch_indices.append((texture_name, pool_idx))

            x = torch.cat(batch_x, dim=0)
            x, q_z_given_x, states = nca(x[..., :3, :, :])

            # Calculate losses
            generate_images = nca.to_rgb(x)
            overflow_loss = (x - x.clamp(-1.0, 1.0)).abs().sum()
            texture_loss, _ = loss_fn(target_images, generate_images)
            # mse_loss = L2_dist(generate_images, target_images)
            mse_loss = L1_dist(generate_images, target_images)
            kl_loss = torch.distributions.kl_divergence(q_z_given_x, nca.p_z).sum(dim=1).mean()
            loss = texture_loss + overflow_loss + kl_loss + mse_loss 

            print('epoch: ', epoch, ' batch: ', batch_idx,
                    'texture_loss: ', texture_loss.item(),
                    'overflow_loss: ', overflow_loss.item(),
                    'kl_loss: ', kl_loss.item(),
                    'mse_loss: ', mse_loss.item())
    
            opt.zero_grad()
            loss.backward()
            for p in nca.parameters():
                if p.grad is not None:  # Check if gradient exists
                    p.grad /= (p.grad.norm() + 1e-8)

            opt.step()
            lr_sched.step()

            with torch.no_grad():
                for i, (texture_name, pool_idx) in enumerate(batch_indices):
                    pools[texture_name][pool_idx] = x[i:i+1].detach()


            if (epoch % config['training']['log_interval'] == 0) or (epoch == iterations - 1):
                with torch.no_grad():
                    generated_state, generated_states = nca.generate(n_samples=config['training']['batch_size'])
                    # Use the final state for visualization
                    outputs = (nca.to_rgb(generated_state[..., :3, :, :])).permute([0, 2, 3, 1]).detach().cpu().numpy()
                outputs = (np.clip(outputs, 0, 1) * 255.0).astype(np.uint8)
                original_imgs = ((target_images.permute(0, 2, 3, 1).detach().cpu().numpy()/2+0.5) * 255.0).astype(np.uint8)
                for idx, (output, texture_name, original_img) in enumerate(zip(outputs, texture_names, original_imgs)):
                    with torch.no_grad():
                        original_tensor = torch.from_numpy(original_img).permute(2, 0, 1).float().to(device)
                        original_tensor = original_tensor.unsqueeze(0)  # Add batch dimension
                        new, q_z_given_x, states = nca(original_tensor)
                        input_to_generate = (np.clip(nca.to_rgb(new[..., :3, :, :]).permute([0, 2, 3, 1]).detach().cpu().numpy(), 0, 1) * 255.0).astype(np.uint8)

                    generated_images = pools[texture_name][:4]
                    imgs = nca.to_rgb(generated_images).permute([0, 2, 3, 1]).detach().cpu().numpy()
                    imgs = (np.clip(imgs, 0, 1) * 255.0).astype(np.uint8)
                    # import pdb; pdb.set_trace()
                    imgs = np.hstack([original_img] + [input_to_generate[0]] + [output] + [imgs[i] for i in range(4)])
                    Image.fromarray(imgs).save(f'{output_dirs[texture_name]}/{texture_name}-epoch-{epoch}.png')

    torch.save(nca.state_dict(), os.path.join(config['experiment_path'], "final_model.pth"))


if __name__ == "__main__":
    args = parser.parse_args()
    with open(args.config, 'r') as stream:
        config = yaml.load(stream, Loader=yaml.FullLoader)

    config['training']['lr'] = 0.001
    config['data_dir'] = args.data_dir
    config['training']['batch_size'] = args.batch_size
    # exp_name = config['experiment_name']
    # exp_path = f'results/{exp_name}/vnca_each/LYM'
    exp_path = args.data_save_dir
    config['experiment_path'] = exp_path
    if not os.path.exists(exp_path):
        os.makedirs(exp_path)

    main(config)

