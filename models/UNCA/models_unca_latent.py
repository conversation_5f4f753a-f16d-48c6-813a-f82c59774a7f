import torch
import random
import numpy as np
from torch.distributions import Normal, Distribution
from torch.utils.checkpoint import checkpoint
from Unet import U_Net, R2U_Net
import torch.nn as nn

# device = torch.device("mps")
device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')


class SimpleUpdateNet(torch.nn.Module):
    """Simple update network for VNCA"""
    def __init__(self, n_channels, hidden_dim=128, output_channels=None):
        super().__init__()
        if output_channels is None:
            output_channels = n_channels

        self.conv1 = torch.nn.Conv2d(n_channels, hidden_dim, kernel_size=3, padding=1)
        self.conv2 = torch.nn.Conv2d(hidden_dim, hidden_dim, kernel_size=1)
        self.conv3 = torch.nn.Conv2d(hidden_dim, output_channels, kernel_size=1)

        torch.nn.init.xavier_normal_(self.conv1.weight)
        torch.nn.init.xavier_normal_(self.conv2.weight)
        torch.nn.init.zeros_(self.conv3.weight)
        self.dropout = torch.nn.Dropout2d(p=0.2)

    def forward(self, x):
        h = self.dropout(torch.relu(self.conv1(x)))
        h = self.dropout(torch.relu(self.conv2(h)))
        return self.conv3(h)


class VNCASobel(torch.nn.Module):
    """Sobel filter perception for VNCA"""
    def __init__(self, n_channels):
        super().__init__()
        self.n_channels = n_channels
        self.device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')

        # Define Sobel filters
        with torch.no_grad():
            ident = torch.tensor([[0.0, 0.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 0.0]], device=self.device)
            sobel_x = torch.tensor([[-1.0, 0.0, 1.0], [-2.0, 0.0, 2.0], [-1.0, 0.0, 1.0]], device=self.device)
            lap = torch.tensor([[0.5, 1.0, 0.5], [1.0, -6.0, 1.0], [0.5, 1.0, 0.5]], device=self.device)
            self.filters = torch.stack([ident, sobel_x, sobel_x.T, lap])

    def forward(self, x):
        b, ch, h, w = x.shape
        y = x.reshape(b * ch, 1, h, w)
        y = torch.nn.functional.pad(y, [1, 1, 1, 1], 'circular')
        y = torch.nn.functional.conv2d(y, self.filters[:, None])

        # Reshape to [b, ch * 4, h, w]
        return y.reshape(b, ch * 4, h, w)
    
class conv_block(nn.Module):
    def __init__(self, ch_in, ch_out):
        super(conv_block, self).__init__()
        self.conv = nn.Sequential(
            nn.Conv2d(ch_in, ch_out, kernel_size=3, stride=1, padding=1, bias=True),
            nn.BatchNorm2d(ch_out),
            nn.ReLU(inplace=True),
            nn.Conv2d(ch_out, ch_out, kernel_size=3, stride=1, padding=1, bias=True),
            nn.BatchNorm2d(ch_out),
            nn.ReLU(inplace=True)
        )
    def forward(self, x):
        return self.conv(x)

class UNetEncoder(nn.Module):
    def __init__(self, in_ch, z_size):
        super(UNetEncoder, self).__init__()
        n1 = 64
        filters = [n1, n1 * 2, n1 * 4, n1 * 8, n1 * 16]
        
        self.Maxpool = nn.MaxPool2d(kernel_size=2, stride=2)
        
        self.Conv1 = conv_block(in_ch, filters[0])
        self.Conv2 = conv_block(filters[0], filters[1])
        self.Conv3 = conv_block(filters[1], filters[2])
        self.Conv4 = conv_block(filters[2], filters[3])
        self.Conv5 = conv_block(filters[3], filters[4])
        
        self.avgpool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Linear(filters[4], z_size * 2) 
        
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.xavier_normal_(m.weight)
                nn.init.zeros_(m.bias)

    def forward(self, x):
        e1 = self.Conv1(x)
        e2 = self.Conv2(self.Maxpool(e1))
        e3 = self.Conv3(self.Maxpool(e2))
        e4 = self.Conv4(self.Maxpool(e3))
        e5 = self.Conv5(self.Maxpool(e4))
        
        z = self.avgpool(e5)
        # import pdb; pdb.set_trace()
        z = z.view(z.size(0), -1)
        z = self.fc(z)
        
        return z


class UNCA(torch.nn.Module):
    """Variational Neural Cellular Automata"""
    def __init__(self, chn, fc_dim, z_size=None, min_steps=8, max_steps=24, update_prob=0.5, **kwargs):

        super(UNCA, self).__init__()
        # self.z_size = z_size*3
        self.chn = chn
        self.z_size = z_size*chn
        self.min_steps = min_steps
        self.max_steps = max_steps
        self.update_prob = update_prob
        self.device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')

        # Encoder network (q(z|x))
        # self.encoder = torch.nn.Sequential(
        #     torch.nn.Conv2d(chn, 32, kernel_size=3, stride=2, padding=1),
        #     torch.nn.ReLU(),
        #     torch.nn.Conv2d(32, 64, kernel_size=3, stride=2, padding=1),
        #     torch.nn.ReLU(),
        #     torch.nn.Conv2d(64, 128, kernel_size=3, stride=2, padding=1),
        #     torch.nn.ReLU(),
        #     torch.nn.AdaptiveAvgPool2d(1),
        #     torch.nn.Flatten(),
        #     torch.nn.Linear(128, self.z_size * 2)  # Output mean and log variance for z_size dimensions
        # )
        # for m in self.encoder.modules():
        #     if isinstance(m, torch.nn.Conv2d):
        #         torch.nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
        #     elif isinstance(m, torch.nn.Linear):
        #         torch.nn.init.xavier_normal_(m.weight)
        #         torch.nn.init.zeros_(m.bias)

        self.encoder_unet = UNetEncoder(in_ch=chn, z_size=self.z_size) 

        self.p_z = Normal(torch.zeros(self.z_size, device=self.device),
                          torch.ones(self.z_size, device=self.device))
        
        self.perception_net = VNCASobel(chn) # 4 kernels

        self.update_net = SimpleUpdateNet(chn * 4, fc_dim, output_channels=chn) # 3 conv


    # def encode(self, x):
    #     """Encode input image to latent distribution q(z|x)"""
    #     z = self.encoder(x)
    #     loc = z[:, :self.z_size]
    #     logsigma = z[:, self.z_size:]
    #     scale=torch.exp(logsigma)+ 1e-6
    #     return Normal(loc=loc, scale=torch.clamp(scale, min=1e-6))

    def encode(self, x):
        """Encode input image to latent distribution q(z|x)"""
        # z = self.encoder(x)
        z = self.encoder_unet(x)
        loc = z[:, :self.z_size]
        logsigma = z[:, self.z_size:]

        return Normal(loc=loc, scale=torch.exp(logsigma))

    def __step(self, state, rand_update_mask=None):
        """Single step of the NCA"""
        perceived = self.perception_net(state) # 4 kernels
        update = self.update_net(perceived) # 3 conv 2 dropout

        state = state + update * rand_update_mask

        return state

    def decode(self, z):
        """Decode latent vector to sequence of states"""
        b = z.shape[0]
        state = z.view(b, -1, 128, 128) #  [8, 3, 128, 128]/[8, 16, 128, 128]
        
        # chn: Number of channels in the cell state
        full_state = torch.zeros(b, self.chn, 128, 128, device=self.device) # [8, 16, 128, 128]
        full_state[:, :state.shape[1]] = state[:, :state.shape[1]]

        # NCA steps
        states = [full_state]
        for _ in range(random.randint(self.min_steps, self.max_steps)):
            rand_mask = None
            if self.update_prob < 1.0:
                rand_mask = (torch.rand((full_state.shape[0], 1, full_state.shape[2], full_state.shape[3]),
                                       device=self.device) < self.update_prob).to(torch.float32)
            full_state = self.__step(full_state, rand_mask)
            
            states.append(full_state)

        return states

    def forward(self, x, n_samples=1):
        """Forward pass for training"""
        q_z_given_x = self.encode(x)

        z = q_z_given_x.rsample((n_samples,))

        states = self.decode(z.reshape(-1, self.z_size))

        return states[-1], q_z_given_x, states

    def generate(self, n_samples=1):
        """Generate samples from prior"""
        z = self.p_z.sample((n_samples,))

        states = self.decode(z)

        return states[-1], states

    def seed(self, n, h=128, w=128):
        return (torch.rand(n, self.chn, h, w, device=self.device) - 0.5) * 0.25


    def to_rgb(self, s):
        """Converts the cell state to RGB. The offset of 0.5 is added so that the valid values are in [0, 1] range."""
        # Make sure we only use the first 3 channels for RGB
        return s[:, :3, :, :] + 0.5

