import torch
import random
import numpy as np
# from Unet import U_Net, R2U_Net
from torch.distributions import Normal, Distribution
from torch.utils.checkpoint import checkpoint
import math
import torch.nn as nn
from functools import partial
# device = torch.device("mps")
device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
from utils import utils
from torch.distributions import Beta

def depthwise_conv(x, filters, padding='circular'):
    """filters: [filter_n, h, w]"""

    b, ch, h, w = x.shape
    y = x.reshape(b * ch, 1, h, w)
    y = torch.nn.functional.pad(y, [1, 1, 1, 1], padding)
    y = torch.nn.functional.conv2d(y, filters[:, None])
    return y.reshape(b, -1, h, w)


def merge_lap(z):
    # This function merges the lap_x and lap_y into a single laplacian filter
    b, c, h, w = z.shape
    z = torch.stack([
        z[:, ::5],
        z[:, 1::5],
        z[:, 2::5],
        z[:, 3::5] + z[:, 4::5]
    ],
        dim=2)  # [b, chn, 4, h, w]
    return z.reshape(b, -1, h, w)  # [b, 4 * chn, h, w]



class SimpleUpdateNet(torch.nn.Module):
    """Simple update network for VNCA"""
    def __init__(self, n_channels, hidden_dim=128, output_channels=None):
        super().__init__()
        if output_channels is None:
            output_channels = n_channels

        self.conv1 = torch.nn.Conv2d(n_channels, hidden_dim, kernel_size=3, padding=1)
        self.conv2 = torch.nn.Conv2d(hidden_dim, hidden_dim, kernel_size=1)
        # self.conv3 = torch.nn.Conv2d(hidden_dim, hidden_dim, kernel_size=1)
        # self.conv2 = torch.nn.Conv2d(hidden_dim, hidden_dim, kernel_size=3, padding=1)
        # self.conv3 = torch.nn.Conv2d(hidden_dim, hidden_dim, kernel_size=3, padding=1)
        self.conv4 = torch.nn.Conv2d(hidden_dim, output_channels, kernel_size=1)


        torch.nn.init.xavier_normal_(self.conv1.weight)
        torch.nn.init.xavier_normal_(self.conv2.weight)
        # torch.nn.init.xavier_normal_(self.conv3.weight)
        torch.nn.init.zeros_(self.conv4.weight)
        self.dropout = torch.nn.Dropout2d(p=0.2)

    def forward(self, x):
        '''
        n_channels: 144 (36*4)
        hidden_dim: 480
        output_channels: 36
        
        H_out = H_in + 2·padding - (kernel_size - 1)
        W_out = W_in + 2·padding - (kernel_size - 1)

        conv1: kernel_size=3,padding=1
        H_out = H_in + 2·1 - (3-1) = H_in + 2 - 2 = H_in
        W_out = W_in + 2·1 - (3-1) = W_in + 2 - 2 = W_in

        conv2: kernel_size=1,padding=0
        H_out = H_in + 2·0 - (1-1) = H_in + 0 - 0 = H_in
        W_out = W_in + 2·0 - (1-1) = W_in + 0 - 0 = W_in

        x: [4, 144, 128, 128]
        h: [4, 480, 128, 128]
        h: [4, 480, 128, 128]
        out: [4, 36, 128, 128]
        '''
        h = self.dropout(torch.relu(self.conv1(x)))
        h = self.dropout(torch.relu(self.conv2(h)))
        # h = self.dropout(torch.relu(self.conv3(h)))

        return self.conv4(h)


class VNCASobel(torch.nn.Module):
    """Sobel filter perception for VNCA"""
    def __init__(self, n_channels):
        super().__init__()
        self.n_channels = n_channels
        self.device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')

        # Define Sobel filters
        with torch.no_grad():
            ident = torch.tensor([[0.0, 0.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 0.0]], device=self.device)
            sobel_x = torch.tensor([[-1.0, 0.0, 1.0], [-2.0, 0.0, 2.0], [-1.0, 0.0, 1.0]], device=self.device)
            lap = torch.tensor([[0.5, 1.0, 0.5], [1.0, -6.0, 1.0], [0.5, 1.0, 0.5]], device=self.device)
            self.filters = torch.stack([ident, sobel_x, sobel_x.T, lap])

    def forward(self, x):
        # Apply filters to each channel
        b, ch, h, w = x.shape
        y = x.reshape(b * ch, 1, h, w)
        y = torch.nn.functional.pad(y, [1, 1, 1, 1], 'circular')
        y = torch.nn.functional.conv2d(y, self.filters[:, None])

        # Reshape to [b, ch * 4, h, w]
        return y.reshape(b, ch * 4, h, w)


class Attention(nn.Module):
    def __init__(self, dim, num_heads=8, qkv_bias=False, attn_drop=0., proj_drop=0.,):
        super().__init__()
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5

        self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)

    def forward(self, x):
        B, N, C = x.shape
        qkv = self.qkv(x).reshape(B, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        q, k, v = qkv.unbind(0)   # make torchscript happy (cannot use tensor as tuple)

        attn = (q @ k.transpose(-2, -1)) * self.scale
        attn = attn.softmax(dim=-1)
        attn = self.attn_drop(attn)

        x = (attn @ v).transpose(1, 2).reshape(B, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)
        return x


class DropPath(nn.Module):
    """Drop paths (Stochastic Depth) per sample  (when applied in main path of residual blocks).
    """
    def __init__(self, drop_prob=None):
        super(DropPath, self).__init__()
        self.drop_prob = drop_prob

    def forward(self, x):
        return drop_path(x, self.drop_prob, self.training)

    def extra_repr(self) -> str:
        return 'p={}'.format(self.drop_prob)

class Mlp(nn.Module):
    def __init__(self, in_features, hidden_features=None, out_features=None, act_layer=nn.GELU, drop=0.):
        super().__init__()
        out_features = out_features or in_features
        hidden_features = hidden_features or in_features
        self.fc1 = nn.Linear(in_features, hidden_features)
        self.act = act_layer()
        self.fc2 = nn.Linear(hidden_features, out_features)
        self.drop = nn.Dropout(drop)

    def forward(self, x):
        x = self.fc1(x)
        x = self.act(x)
        # x = self.drop(x)
        # commit this for the orignal BERT implement 
        x = self.fc2(x)
        x = self.drop(x)
        return x


class Block(nn.Module):

    def __init__(self, dim, num_heads, mlp_ratio=4., qkv_bias=False, drop=0., attn_drop=0.,
                 drop_path=0., act_layer=nn.GELU, norm_layer=nn.LayerNorm):
        super().__init__()
        self.norm1 = norm_layer(dim)
        self.attn = Attention(dim, num_heads=num_heads, qkv_bias=qkv_bias, attn_drop=attn_drop, proj_drop=drop)
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()
        self.norm2 = norm_layer(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = Mlp(in_features=dim, hidden_features=mlp_hidden_dim, act_layer=act_layer, drop=drop)

    def forward(self, x):
        x = x + self.drop_path(self.attn(self.norm1(x)))
        x = x + self.drop_path(self.mlp(self.norm2(x)))
        return x


class MVNCA(torch.nn.Module):
    """Variational Neural Cellular Automata"""
    def __init__(self, chn, fc_dim, z_size=None, min_steps=8, max_steps=24, update_prob=0.5, **kwargs):

        super(MVNCA, self).__init__()
        self.embedding_chn = kwargs['embedding_chn']
        self.chn = chn
        self.min_steps = min_steps
        self.max_steps = max_steps
        self.img_size = kwargs['img_size']
        self.z_size = (chn - 4) * self.img_size * self.img_size
        self.update_prob = update_prob
        self.embed_dim = 768
        self.device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')

       # Encoder network (q(z|x))
        self.encoder = torch.nn.Sequential(
            torch.nn.Flatten(),  # [16,36,768] -> [16, 36*768]
            torch.nn.Linear(self.embedding_chn * self.embed_dim, 64),
            torch.nn.ReLU(), 
            torch.nn.Linear(64, 128),
            torch.nn.ReLU(),
            torch.nn.Linear(128, 256),
            torch.nn.ReLU(),

            torch.nn.Linear(256, self.z_size * 2),
        )
        for m in self.encoder.modules():
            if isinstance(m, torch.nn.Linear):
                torch.nn.init.xavier_normal_(m.weight) 
                torch.nn.init.zeros_(m.bias)

         # Transformer encoder
        depth = 6
        dpr = [x.item() for x in torch.linspace(0, 0.0, depth)]  # dropout 0.0, depth 12
        self.transformer_encoder = nn.Sequential(*[
            Block(dim=self.embed_dim, num_heads=4, mlp_ratio=4.0, qkv_bias=True,
                  drop=0, attn_drop=0.0, drop_path=dpr[i], norm_layer=partial(nn.LayerNorm, eps=1e-6))
            for i in range(depth)
        ])

        self.p_z = Normal(torch.zeros(self.z_size, device=self.device),
                          torch.ones(self.z_size, device=self.device))
        # self.alpha = 2
        # self.beta = 2
        # self.p_z = Beta(self.alpha * torch.ones(self.z_size, device=self.device), # [0, 1]
        #                self.beta * torch.ones(self.z_size, device=self.device))
        
        self.perception_net = VNCASobel(chn) # 4 kernels

        self.update_net = SimpleUpdateNet(chn * 4, fc_dim, output_channels = chn) # 3 conv

        self.img_fc = nn.Linear(self.embed_dim, self.embed_dim)
        nn.init.eye_(self.img_fc.weight)
        nn.init.zeros_(self.img_fc.bias)

    def mask_img_name(self, img_token, name_token, mask_prob=0.75):
        img_token = self.img_fc(img_token)

        img_mask = (torch.rand(img_token.size(1)) > mask_prob).float()
        img_mask = img_mask.unsqueeze(-1).expand_as(img_token)
        img_token = img_token * img_mask
        name_mask = (torch.rand(name_token.size(1)) > mask_prob).float()
        name_mask = name_mask.unsqueeze(-1).expand_as(name_token)
        name_token = name_token * name_mask

        null_token = torch.zeros(img_token.size(0), 1, img_token.size(-1))
        target_tokens = torch.cat([null_token, img_token, name_token, null_token], dim=1)
        return target_tokens

    def mask_to_predict(self, img_token, name_token, mask_img=1):
        img_token = self.img_fc(img_token)

        if mask_img:
            img_mask = torch.zeros_like(img_token)
            name_mask = torch.ones_like(name_token)
        else:
            name_mask = torch.zeros_like(name_token)
            img_mask = torch.ones_like(img_token)
        img_token = img_token * img_mask
        name_token = name_token * name_mask

        null_token = torch.zeros(img_token.size(0), 1, img_token.size(-1))
        target_tokens = torch.cat([null_token, img_token, name_token, null_token], dim=1)
        return target_tokens

    def encode(self, x):
        """Encode input image to latent distribution q(z|x)"""
        z = self.encoder(x)
        loc = z[:, :self.z_size]
        logsigma = z[:, self.z_size:]
        scale=torch.exp(logsigma) #+ 1e-6
        return Normal(loc=loc, scale=scale)

        # z = self.encoder(x)
        # alpha = torch.exp(z[:, :self.z_size]) + 1  # alpha > 1
        # beta = torch.exp(z[:, self.z_size:]) + 1   # beta > 1
        # return Beta(alpha, beta)

    def __step(self, state, rand_update_mask=None):
        """Single step of the NCA"""
        perceived = self.perception_net(state) # 4 kernels
        update = self.update_net(perceived) # 3 conv 2 dropout

        state = state + update * rand_update_mask

        return state

    def __time_encoder(self, seq_len, d=4, n=1000.0):
        """Encode time as a vector"""
        position = torch.arange(0, seq_len, dtype=torch.float, device=self.device).unsqueeze(1)
    
        div_term = torch.exp(torch.arange(0, d, 2, dtype=torch.float, device=self.device) * (-math.log(n) / d))
        
        pe = torch.zeros(seq_len, d, device=self.device)
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        return pe.unsqueeze(-1).unsqueeze(-1).expand(seq_len, d, 128, 128)

    def decode(self, z, step_n=None):
        """Decode latent vector to sequence of states"""
        if step_n is None:
            step_n = random.randint(self.min_steps, self.max_steps)

        b = z.shape[0]
        state = z.view(b, -1, 128, 128) 
        
        # chn: Number of channels in the cell state
        full_state = torch.zeros(b, self.chn, 128, 128, device=self.device) 
        full_state[:, :state.shape[1]] = state[:, :state.shape[1]]

        # NCA steps
        states = [full_state]
        for t in range(step_n):
            # time_embeddings = self.__time_encoder(seq_len=b, d=4, n=t+1)
            rand_mask = None
            if self.update_prob < 1.0:
                rand_mask = (torch.rand((full_state.shape[0], 1, full_state.shape[2], full_state.shape[3]),
                                       device=self.device) < self.update_prob).to(torch.float32)
            # full_state[:, -8:-4,:,:] = time_embeddings 
            full_state = self.__step(full_state, rand_mask)
            
            states.append(full_state)

        return states

    def forward(self, x, n_samples=1):
        """Forward pass for training"""
        x = self.transformer_encoder(x)

        q_z_given_x = self.encode(x)

        z = q_z_given_x.rsample((n_samples,))
        z = z.view(x.size(0), -1, self.img_size, self.img_size)

        states = self.decode(z)

        return states[-1], q_z_given_x, z

    def generate(self, n_samples=1):
        """Generate samples from prior"""
        z = self.p_z.sample((n_samples,))
        z = z.view(n_samples, -1, self.img_size, self.img_size)

        states = self.decode(z)

        return states[-1], states

    # def seed(self, n, h=128, w=128):
    #     return (torch.rand(n, self.chn, h, w, device=self.device) - 0.5) # * 8

    def to_rgb(self, s):
        """Converts the cell state to RGB. The offset of 0.5 is added so that the valid values are in [0, 1] range."""
        # Make sure we only use the first 3 channels for RGB
        return s[:, :3, :, :] * 0.25  # 99.994% [-4, 4] for N(0,1)

