import os
import shutil
import argparse
import yaml
from tqdm import tqdm
import torch
import utils.utils as utils
import numpy as np
import torch.nn as nn 
import wandb
from PIL import Image
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
import matplotlib.pyplot as plt

import umap
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from transformers import BertModel, BertTokenizer

from utils.utils import *
from utils.loss import TextureLoss
from models_mvnca_decoder import  MVNCA
from utils.dataset_omi import TextureDataset

# from torch import distributed
# from torch.utils.data.distributed import DistributedSampler

parser = argparse.ArgumentParser()
parser.add_argument('--data_dir', type=str, default='data/images_50_model_nine', help="Texture images directory")
parser.add_argument('--data_save_dir', type=str, default='results/MVNCA/mvnca_nine_mask50_omi_decoder', help="Saved images directory")
parser.add_argument('--max_size', type=int, default=128, help="Max size of the image")
parser.add_argument('--config', type=str, default='configs/MVNCA.yml', help="Path to the config file")
parser.add_argument('--batch_size', type=int, default=4, help="Number of images per batch")
parser.add_argument('--lr', type=float, default=1e-5, help="Learning rate")
args = parser.parse_args()



def main(config):

    device = torch.device(config['device'])
    # device = torch.device(config.get('device', 'cuda:0') if torch.cuda.is_available() else 'cpu')
    # device = torch.device(f'cuda:{args.local_rank}' if torch.cuda.is_available() else 'cpu')
    config['loss']['attr']['device'] = device
    config['model']['attr']['device'] = device
    loss_fn = TextureLoss(**config['loss']['attr']).to(device)

    # dataloader
    dataset = TextureDataset(config['data_dir'], max_size=args.max_size)
    # train_sampler = DistributedSampler(dataset)  # multi gpu
    dataloader = DataLoader(dataset, batch_size=config['training']['batch_size'],
                          shuffle=True, num_workers=4, drop_last=False) #, sampler=train_sampler

    nca = MVNCA(**config['model']['attr']).to(device)
    opt = torch.optim.Adam(nca.parameters(), config['training']['lr'])

    if 'type' not in config['training']['scheduler'] or config['training']['scheduler']['type'] == 'MultiStep':
        lr_sched = torch.optim.lr_scheduler.MultiStepLR(opt, **config['training']['scheduler']['attr'])
    elif config['training']['scheduler']['type'] == 'Cyclic':
        lr_sched = torch.optim.lr_scheduler.CyclicLR(opt, **config['training']['scheduler']['attr'])

    iterations = config['training']['iterations']
    alpha = config['training']['overflow_weight']
    step_range = config['training']['nca']['step_range']
    inject_seed_step = config['training']['nca']['inject_seed_step']
    pool_size = config['training']['nca']['pool_size']
    batch_size = config['training']['batch_size']

    # output dir
    texture_names = [os.path.splitext(f)[0] for f in os.listdir(config['data_dir'])
                   if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
    output_dirs = {}
    for name in texture_names:
        output_dir = os.path.join(config['experiment_path'], name)
        os.makedirs(output_dir, exist_ok=True)
        output_dirs[name] = output_dir


    losses = {
        'texture_loss': [],
        'overflow_loss': [],
        'kl_loss': [],
        'mse_loss': [],
        'total_loss': []
    }

    for epoch in tqdm(range(iterations)):
        epoch_losses = {k: [] for k in losses.keys()}

        for batch_idx, (img, img_token, name_token, texture_name) in enumerate(dataloader):
            # print(texture_name[0], ': img', img[0][0][0][0], ': img token 0', img_token[0][0][0], ': img token 1', img_token[0][1][0], ': name token 0', name_token[0][0][0], ': name token 1', name_token[0][1][0])
            # print(texture_name[1], ': img', img[1][0][0][0], ': img token 0', img_token[1][0][0], ': img token 1', img_token[1][1][0], ': name token 0', name_token[1][0][0], ': name token 1', name_token[1][1][0])
            # print(texture_name[2], ': img', img[2][0][0][0], ': img token 0', img_token[2][0][0], ': img token 1', img_token[2][1][0], ': name token 0', name_token[2][0][0], ': name token 1', name_token[2][1][0])
            # print(texture_name[3], ': img', img[3][0][0][0], ': img token 0', img_token[3][0][0], ': img token 1', img_token[3][1][0], ': name token 0', name_token[3][0][0], ': name token 1', name_token[3][1][0])
            # import pdb; pdb.set_trace()
            #  texture_name[0], img[0][0][0][0], img_token[0][0][0], name_token[0][0][0]
            # img[1][0][0][0], img_token[1][0][0], name_token[1][0][0], texture_name[1]
            target_tokens = nca.mask_img_name(img_token, name_token, mask_prob=0.50)
            target_images = img.to(device)
            # if epoch % inject_seed_step == 0:
            #     # target_images[:1] = (torch.rand_like(target_images[:1]) - 0.5) * 0.25
            #     target_process[:1][..., :3+args.latent_dim, :, :] = nca.seed(1)

            generates, q_z_given_x, _ = nca(target_tokens)

            # Calculate losses
            overflow_loss = (generates - generates.clamp(-5.0, 5.0)).abs().sum() # 99.994% [-4, 4] for N(0,1)
            generate_images = nca.to_rgb(generates)
            texture_loss, _ = loss_fn(target_images, generate_images)
            mse_loss = L1_dist(generate_images, target_images)
            kl_loss = torch.distributions.kl_divergence(q_z_given_x, nca.p_z).sum(dim=1).mean()
            loss = overflow_loss +  texture_loss # + mse_loss #  + kl_loss   overflow_loss + 

            print('epoch: ', epoch, ' batch: ', batch_idx,
                    'texture_loss: ', texture_loss.item(),
                    'overflow_loss: ', overflow_loss.item(),
                    'kl_loss: ', kl_loss.item(),
                    'mse_loss: ', mse_loss.item())
            current_losses = {
                'texture_loss': texture_loss.item(),
                'overflow_loss': overflow_loss.item(),
                'kl_loss': kl_loss.item(),
                'mse_loss': mse_loss.item(),
                'total_loss': loss.item()
            }
            for k, v in current_losses.items():
                epoch_losses[k].append(v)
    
            opt.zero_grad()
            loss.backward()

            for p in nca.parameters():
                if p.grad is not None:  # Check if gradient exists
                    p.grad /= (p.grad.norm() + 1e-8)

            opt.step()
            lr_sched.step()

            if (epoch % config['training']['log_interval'] == 0) or (epoch == iterations - 1):
                with torch.no_grad():
                    generated_state, generated_states = nca.generate(n_samples=args.batch_size)
                    ori_new, _, z = nca(target_tokens)
                    img_masked_tokens = nca.mask_to_predict(img_token, name_token, mask_img=1)
                    img_mask_new, _, z_img_mask = nca(img_masked_tokens)
                    name_masked_tokens = nca.mask_to_predict(img_token, name_token, mask_img=0)
                    name_mask_new, _, z_name_mask = nca(name_masked_tokens)
                    # save_video(z, output_dirs[texture_name[0]], f'{texture_name[0]}-epoch-{epoch}', nca_model=nca, video_length=10)
                    # save_video(z_img_mask, output_dirs[texture_name[0]], f'{texture_name[0]}-epoch-{epoch}_img_mask',nca_model=nca, video_length=10)
                    # save_video(z_name_mask, output_dirs[texture_name[0]], f'{texture_name[0]}-epoch-{epoch}_name_mask',nca_model=nca, video_length=10)

                ori_generates = (nca.to_rgb(ori_new)).permute([0, 2, 3, 1]).detach().cpu().numpy()
                ori_generates = ((np.clip(ori_generates, -1, 1)+1)/2.0 * 255.0).astype(np.uint8)

                img_mask_generates = (nca.to_rgb(img_mask_new)).permute([0, 2, 3, 1]).detach().cpu().numpy()
                img_mask_generates = ((np.clip(img_mask_generates, -1, 1)+1)/2.0 * 255.0).astype(np.uint8)

                name_mask_generates = (nca.to_rgb(name_mask_new)).permute([0, 2, 3, 1]).detach().cpu().numpy()
                name_mask_generates = ((np.clip(name_mask_generates, -1, 1)+1)/2.0 * 255.0).astype(np.uint8)

                noise_generates = (nca.to_rgb(generated_state)).permute([0, 2, 3, 1]).detach().cpu().numpy()
                noise_generates = ((np.clip(noise_generates, -1, 1)+1)/2.0 * 255.0).astype(np.uint8)

                original_imgs = ((target_images.permute(0, 2, 3, 1).detach().cpu().numpy()+1)/2.0 * 255.0).astype(np.uint8)
                for idx, (original_img, ori_generate, noise_generate, img_mask_generate, name_mask_generate, name) in enumerate(zip(original_imgs, ori_generates, noise_generates, img_mask_generates, name_mask_generates, texture_name)):
                    imgs = np.hstack([original_img] + [ori_generate] + [noise_generate] + [img_mask_generate] + [name_mask_generate])
                    Image.fromarray(imgs).save(f'{output_dirs[name]}/{name}-epoch-{epoch}.png')
                    
        if (epoch % config['training']['log_interval'] == 0) or (epoch == iterations - 1):
            # Plot latent space projections
            all_means = []
            all_img_means = []
            all_name_means = []
            all_texture_names = []
            with torch.no_grad():
                for batch_idx, (img, img_token, name_token, texture_name) in enumerate(dataloader):
                    # target_tokens = mask_img_name(img_token, name_token, mask_prob=0.5).to(device)
                    img_tokens = nca.mask_to_predict(img_token, name_token, mask_img=0)
                    name_tokens = nca.mask_to_predict(img_token, name_token, mask_img=1)
                    null_token = torch.zeros(img_token.size(0), 1, img_token.size(-1))
                    all_tokens = torch.cat([null_token, img_token, name_token, null_token], dim=1).to(device)
                    _, all, _ = nca(all_tokens)
                    all = all.loc.detach().cpu().numpy()
                    _, img, _ = nca(img_tokens)
                    img = img.loc.detach().cpu().numpy()
                    _, name, _ = nca(name_tokens)
                    name = name.loc.detach().cpu().numpy()
                    all_means.append(all)
                    all_img_means.append(img)
                    all_name_means.append(name)
                    all_texture_names.extend(texture_name)
            all_means = np.concatenate(all_means, axis=0)
            all_img_means = np.concatenate(all_img_means, axis=0)
            all_name_means = np.concatenate(all_name_means, axis=0)

            utils.plot_latent_projections_new(all_means, all_texture_names, epoch, 
                                'utils/output50_omic_decoder/all', method='umap')
            # utils.plot_latent_projections_new(all_means, all_texture_names, epoch, 
            #                     'utils/output50/all', method='pca')
            utils.plot_latent_projections_new(all_img_means, all_texture_names, epoch, 
                                'utils/output50_omic_decoder/img', method='umap')
            # utils.plot_latent_projections_new(all_img_means, all_texture_names, epoch, 
            #                     'utils/output50/img', method='pca')
            utils.plot_latent_projections_new(all_name_means, all_texture_names, epoch, 
                                'utils/output50_omic_decoder/name', method='umap')
            # utils.plot_latent_projections_new(all_name_means, all_texture_names, epoch, 
            #                     'utils/output50/name', method='pca')


            for k in losses.keys():
                avg_loss = sum(epoch_losses[k]) / len(epoch_losses[k])
                losses[k].append(avg_loss)
            if (epoch + 1) % config['training'].get('plot_interval', 1) == 0:
                utils.plot_training_curves(losses, epoch, config['experiment_path'])
            # utils.plot_training_curves(losses, epoch, config['experiment_path'])


            torch.save(nca.state_dict(), os.path.join(config['experiment_path'], f"final_model_epoch{epoch}.pth"))


if __name__ == "__main__":
    # args = parser.parse_args()
    with open(args.config, 'r') as stream:
        config = yaml.load(stream, Loader=yaml.FullLoader)

    config['training']['lr'] = args.lr
    config['data_dir'] = args.data_dir
    config['training']['batch_size'] = args.batch_size
    # exp_name = config['experiment_name']
    # exp_path = f'results/{exp_name}/vnca_each/LYM'
    exp_path = args.data_save_dir
    config['experiment_path'] = exp_path
    if not os.path.exists(exp_path):
        os.makedirs(exp_path)

    main(config)

# CUDA_VISIBLE_DEVICES=1 python3 run_mvnca_decoder.py

# module load mamba
# mamba activate mvnca
# cd /home/<USER>/wanglab/tmhqxl20/NCA-FM 
