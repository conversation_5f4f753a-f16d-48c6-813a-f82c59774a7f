import os
import shutil
import argparse
import yaml
from tqdm import tqdm
import torch
import utils.utils as utils
import numpy as np
import torch.nn as nn 
import wandb
from PIL import Image
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
import matplotlib.pyplot as plt

import umap
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from transformers import BertModel, BertTokenizer

from utils.utils import *
from utils.loss import TextureLoss
from models_mvnca import  MVNCA
from utils.dataset_bert import TextureDataset

from torch import distributed
from torch.utils.data.distributed import DistributedSampler

parser = argparse.ArgumentParser()
parser.add_argument('--data_dir', type=str, default='data/images_50_model_nine', help="Texture images directory")
parser.add_argument('--data_save_dir', type=str, default='results/MVNCA/mvnca_nine', help="Saved images directory")
parser.add_argument('--max_size', type=int, default=128, help="Max size of the image")
parser.add_argument('--config', type=str, default='configs/MVNCA.yml', help="Path to the config file")
parser.add_argument('--batch_size', type=int, default=16, help="Number of images per batch")

# # multi gpu
parser.add_argument('--local-rank', type=int, default=0, help='local rank for distributed training')
parser.add_argument('--world-size', type=int, default=2, help='world size for distributed training')
args = parser.parse_args()

# local_rank = int(os.environ.get('LOCAL_RANK', -1))
# world_size = int(os.environ.get('WORLD_SIZE', 1))

if args.local_rank != -1:
    distributed.init_process_group(backend="nccl")
    torch.cuda.set_device(args.local_rank)

# python -m torch.distributed.launch --nproc_per_node=2 multi_main.py

def main(config):

    # device = torch.device(config['device'])
    device = torch.device(f'cuda:{args.local_rank}' if torch.cuda.is_available() else 'cpu')
    config['loss']['attr']['device'] = device
    config['model']['attr']['device'] = device
    loss_fn = TextureLoss(**config['loss']['attr']).to(device)

    # dataloader
    dataset = TextureDataset(config['data_dir'], max_size=args.max_size)
    train_sampler = DistributedSampler(dataset)  # multi gpu
    dataloader = DataLoader(dataset, batch_size=config['training']['batch_size'],
                          shuffle=True, num_workers=4, drop_last=False, sampler=train_sampler)

    nca = MVNCA(**config['model']['attr']).to(device)
    opt = torch.optim.Adam(nca.parameters(), config['training']['lr'])

    if 'type' not in config['training']['scheduler'] or config['training']['scheduler']['type'] == 'MultiStep':
        lr_sched = torch.optim.lr_scheduler.MultiStepLR(opt, **config['training']['scheduler']['attr'])
    elif config['training']['scheduler']['type'] == 'Cyclic':
        lr_sched = torch.optim.lr_scheduler.CyclicLR(opt, **config['training']['scheduler']['attr'])

    iterations = config['training']['iterations']
    alpha = config['training']['overflow_weight']
    step_range = config['training']['nca']['step_range']
    inject_seed_step = config['training']['nca']['inject_seed_step']
    pool_size = config['training']['nca']['pool_size']
    batch_size = config['training']['batch_size']

    # output dir
    texture_names = [os.path.splitext(f)[0] for f in os.listdir(config['data_dir'])
                   if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
    output_dirs = {}
    for name in texture_names:
        output_dir = os.path.join(config['experiment_path'], name)
        os.makedirs(output_dir, exist_ok=True)
        output_dirs[name] = output_dir


    for epoch in tqdm(range(iterations)):
        for batch_idx, (img, img_token, name_token, texture_name) in enumerate(dataloader):
            target_tokens = mask_img_name(img_token, name_token, mask_prob=0.5).to(device)
            target_images = img.to(device)
            # target_tokens = mask_img_name(img_token, name_token, mask_prob=0.5).cuda(args.local_rank) ####
            # target_images = img.cuda(args.local_rank)
            # import pdb; pdb.set_trace()
            # if epoch % inject_seed_step == 0:
            #     # target_images[:1] = (torch.rand_like(target_images[:1]) - 0.5) * 0.25
            #     target_process[:1][..., :3+args.latent_dim, :, :] = nca.seed(1)

            generates, q_z_given_x, states = nca(target_tokens)

            # Calculate losses
            overflow_loss = (generates - generates.clamp(-1.0, 1.0)).abs().sum()
            generate_images = nca.to_rgb(generates)
            texture_loss, _ = loss_fn(target_images, generate_images)
            mse_loss = L1_dist(generate_images, target_images)
            kl_loss = torch.distributions.kl_divergence(q_z_given_x, nca.p_z).sum(dim=1).mean()
            loss = overflow_loss + texture_loss # + mse_loss #  + kl_loss

            print('epoch: ', epoch, ' batch: ', batch_idx,
                    'texture_loss: ', texture_loss.item(),
                    'overflow_loss: ', overflow_loss.item(),
                    'kl_loss: ', kl_loss.item(),
                    'mse_loss: ', mse_loss.item())
    
            opt.zero_grad()
            loss.backward()
            for p in nca.parameters():
                if p.grad is not None:  # Check if gradient exists
                    p.grad /= (p.grad.norm() + 1e-8)

            opt.step()
            lr_sched.step()

            if (epoch % config['training']['log_interval'] == 0) or (epoch == iterations - 1):
                with torch.no_grad():
                    generated_state, generated_states = nca.generate(n_samples=args.batch_size)
                    # Use the final state for visualization
                    outputs = (nca.to_rgb(generated_state)).permute([0, 2, 3, 1]).detach().cpu().numpy()
                    outputs = (np.clip(outputs, 0, 1) * 255.0).astype(np.uint8)

                    new, q_z_given_x, states = nca(target_tokens)
                generates = (nca.to_rgb(new)).permute([0, 2, 3, 1]).detach().cpu().numpy()
                generates = (np.clip(generates, 0, 1) * 255.0).astype(np.uint8)
                original_imgs = ((target_images.permute(0, 2, 3, 1).detach().cpu().numpy()/2+0.5) * 255.0).astype(np.uint8)
                for idx, (output, original_img, generate_image, texture_name) in enumerate(zip(outputs, original_imgs, generates, texture_names)):
                    imgs = np.hstack([original_img] + [generate_image] + [output])
                    Image.fromarray(imgs).save(f'{output_dirs[texture_name]}/{texture_name}-epoch-{epoch}.png')

                # Plot latent space projections
                all_z_means = []
                all_texture_names = []
                with torch.no_grad():
                    for batch_idx, (img, img_token, name_token, texture_name) in enumerate(dataloader):
                        target_tokens = mask_img_name(img_token, name_token, mask_prob=0.5).cuda(args.local_rank) #.to(device)
                        # null_token = torch.zeros(img_token.size(0), 1, 768*2)
                        # target_tokens = torch.cat([null_token, img_token, name_token, null_token], dim=1).to(device)
                        x = nca.transformer_encoder(target_tokens)
                        q_z_given_x = nca.encode(x)
                        z_mean = q_z_given_x.loc.detach().cpu().numpy()
                        all_z_means.append(z_mean)
                        all_texture_names.extend(texture_name)
                all_z_means = np.concatenate(all_z_means, axis=0)
                # import pdb; pdb.set_trace()

                # utils.plot_latent_projections(all_z_means, all_texture_names, epoch, 
                #                     config['experiment_path'], method='umap')
                # utils.plot_latent_projections(all_z_means, all_texture_names, epoch, 
                #                     config['experiment_path'], method='pca')
                utils.plot_latent_projections_new(all_z_means, all_texture_names, epoch, 
                                    config['experiment_path'], method='umap')
                utils.plot_latent_projections_new(all_z_means, all_texture_names, epoch, 
                                    config['experiment_path'], method='pca')

    torch.save(nca.state_dict(), os.path.join(config['experiment_path'], "final_model.pth"))


if __name__ == "__main__":
    # args = parser.parse_args()
    with open(args.config, 'r') as stream:
        config = yaml.load(stream, Loader=yaml.FullLoader)

    config['training']['lr'] = 0.001
    config['data_dir'] = args.data_dir
    config['training']['batch_size'] = args.batch_size
    # exp_name = config['experiment_name']
    # exp_path = f'results/{exp_name}/vnca_each/LYM'
    exp_path = args.data_save_dir
    config['experiment_path'] = exp_path
    if not os.path.exists(exp_path):
        os.makedirs(exp_path)

    main(config)

# CUDA_VISIBLE_DEVICES=2 python3 run_mvnca.py

# module load mamba
# mamba activate mvnca
# cd /home/<USER>/wanglab/tmhqxl20/NCA-FM 
