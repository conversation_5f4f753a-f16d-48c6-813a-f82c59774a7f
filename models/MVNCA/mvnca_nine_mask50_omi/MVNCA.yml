experiment_name: "MVNCA"
description: "Training VNCA model on batch images."
device: "cuda:0"

loss:
  attr:
    loss_type: "OT"

model:
  type: "MVNCA"
  noise_levels: { default: 0.25 }
  attr:
    chn: 16 # nca chn 
    fc_dim: 480 # 480
    img_size: 128 # 224
    z_size: 16384 # 50176  128*128
    min_steps: 8
    max_steps: 24
    update_prob: 0.5
    embedding_chn: 36 # 16(4*4) + 18 +2 token chn

training:
  device: "cuda:0"
  lr: 1e-5
  batch_size: 16
  iterations: 2000
  overflow_weight: 10.0
  log_interval: 100

  scheduler:
    type: "MultiStep"
    attr:
      milestones: [ 1000, 2000 ]
      gamma: 0.5

  nca:
    pool_size: 12
    step_range: [ 32, 128 ]
    inject_seed_step: 8
