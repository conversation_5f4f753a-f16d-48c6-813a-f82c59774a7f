import torch
from transformers import BertModel, AutoTokenizer
import torch.nn as nn
import os
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
# from gigapath.pipeline import run_inference_with_tile_encoder

import re, os
from PIL import Image
from einops import rearrange

import umap
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import numpy as np
from tqdm import tqdm
from sklearn.preprocessing import MinMaxScaler


class TextureDataset(Dataset):
    def __init__(self, data_dir, max_size=128):
        self.image_paths = [os.path.join(data_dir, f) for f in os.listdir(data_dir)
                          if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        self.transform = transforms.Compose([
            # transforms.ToPILImage(),
            transforms.Resize(max_size),
            transforms.ToTensor(), # C H W, i.e., permute(2, 0, 1)
            transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
            # transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])

        ])
        self.max_size = max_size
        self.tokenizer = AutoTokenizer.from_pretrained('bionlp/bluebert_pubmed_mimic_uncased_L-12_H-768_A-12')
        self.bert_model =  BertModel.from_pretrained('bionlp/bluebert_pubmed_mimic_uncased_L-12_H-768_A-12')
        self.bert_model.eval() 
        self.embed_dim = 768
        # self.img_fc = nn.Linear(self.embed_dim, self.embed_dim)
        # nn.init.eye_(self.img_fc.weight)
        # nn.init.zeros_(self.img_fc.bias)

    def _generate_name_embeddings(self, texture_name):
        # Load pre-trained BERT model and embed tokenizer
        name_vectors = {}
        sentence = self.name2sentence(texture_name)
        tokens = self.tokenizer(sentence, max_length=18, padding="max_length", return_tensors='pt', truncation=True)
        with torch.no_grad(): 
            outputs = self.bert_model(**tokens)
        name_vectors = outputs.last_hidden_state #.repeat(1, 1, 2)

        # original_shape = name_vectors.shape
        # vectors_np = name_vectors.cpu().numpy().reshape(-1, original_shape[-1])
        # scaler = MinMaxScaler(feature_range=(0, 1))
        # vectors_np = scaler.fit_transform(vectors_np)
        # vectors_np = vectors_np.reshape(original_shape)
        # name_vectors = torch.tensor(vectors_np, dtype=torch.float32)

        # embed position
        seq_len = name_vectors.size(1)
        pos_emb = self.build_sincos_posemb(seq_len=seq_len, embed_dim=self.embed_dim) 
        vectors = name_vectors + pos_emb

        return vectors.squeeze(0)
    
    def name2sentence(self, name):
        category_descriptions = {
            'ADI': 'adipose tissue',
            'BACK': 'background',
            'DEB': 'debris',
            'LYM': 'lymphocyte infiltration',
            'MUS': 'smooth muscle', 
            'MUC': 'mucus',
            'NORM': 'normal tissue',
            'STR': 'stromal cell',
            'TUM': 'tumor cell'
        }
        tokens = name.split('-')
        category = tokens[0]
        category_desc = category_descriptions.get(category, category)
        sentence = f"This is an HE staining image of {category_desc}." # ' '.join(name)
        return sentence

    def build_sincos_posemb(self, seq_len, embed_dim, temperature=10000.):
        position = torch.arange(seq_len, dtype=torch.float32).unsqueeze(1)
        dim_t = torch.arange(0, embed_dim, 2, dtype=torch.float32)
        dim_t = temperature ** (dim_t / embed_dim)
        pos_x = position / dim_t
        pos_emb = torch.stack([torch.sin(pos_x), torch.cos(pos_x)], dim=-1).flatten(-2)
        pos_emb = pos_emb.unsqueeze(0)  # Add batch dimension [1, seq_len, embed_dim]
        return pos_emb

    def build_2d_sincos_posemb(self, h, w, embed_dim, temperature=10000.):
        """Sine-cosine positional embeddings from MoCo-v3
        Source: https://github.com/facebookresearch/moco-v3/blob/main/vits.py
        """
        grid_w = torch.arange(w, dtype=torch.float32)
        grid_h = torch.arange(h, dtype=torch.float32)
        grid_w, grid_h = torch.meshgrid(grid_w, grid_h, indexing='ij')
        assert embed_dim % 4 == 0, 'Embed dimension must be divisible by 4 for 2D sin-cos position embedding'
        pos_dim = embed_dim // 4
        omega = torch.arange(pos_dim, dtype=torch.float32) / pos_dim
        omega = 1. / (temperature ** omega)
        out_w = torch.einsum('m,d->md', [grid_w.flatten(), omega])
        out_h = torch.einsum('m,d->md', [grid_h.flatten(), omega])
        pos_emb = torch.cat([torch.sin(out_w), torch.cos(out_w), torch.sin(out_h), torch.cos(out_h)], dim=1)[None, :, :]
        pos_emb = rearrange(pos_emb, 'b (h w) d -> b d h w', h=h, w=w, d=embed_dim)
        return pos_emb
        

    def _generate_image_embeddings(self, name):
        encode_path = f"data/tile_image_omiclip_{self.max_size}/images_all_one_tile_encode/{name}_encode.pt"
        vectors = torch.load(encode_path, weights_only=True)
        # import pdb; pdb.set_trace()

        # bluebert normalize
        # vectors_np = vectors.cpu().numpy() if isinstance(vectors, torch.Tensor) else vectors
        # scaler = MinMaxScaler(feature_range=(0, 1))
        # vectors_np = scaler.fit_transform(vectors_np) # column min max
        # vectors = torch.tensor(vectors_np, dtype=torch.float32)

        # vectors = self.img_fc(vectors)

        grid_size = (4, 4)
        h, w = grid_size
        pos_emb = self.build_2d_sincos_posemb(h=h, w=w, embed_dim=self.embed_dim) # [1, 768, h, w]
        vectors = vectors.view(1, self.embed_dim, h, w)
        vectors = vectors + pos_emb 
        vectors = vectors.flatten(2).transpose(1, 2)
        return vectors.squeeze(0)
    
    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        img = Image.open(self.image_paths[idx]).convert('RGB')
        img = self.transform(img)
        texture_name = os.path.splitext(os.path.basename(self.image_paths[idx]))[0]
        name_token = self._generate_name_embeddings(texture_name) # [batch_size, sequence_length, hidden_dim(768)]
        img_token = self._generate_image_embeddings(texture_name)
        # import pdb; pdb.set_trace()

        return img, img_token, name_token, texture_name



def plot_embeddings(dataset, output_dir, method='both', batch_size=16):
    """
    Plot UMAP and/or PCA visualizations of img_tokens and name_tokens
    
    Args:
        dataset: TextureDataset instance
        output_dir: Directory to save plots
        method: str, one of ['umap', 'pca', 'both']
        batch_size: Number of samples to process at once
    """
    # Validate method parameter
    method = method.lower()
    print("Loading data...")
    
    # Create smaller batch dataloader to avoid opening too many files at once
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=False)
    
    # Initialize lists to store all data
    all_img_tokens = []
    all_name_tokens = []
    all_texture_names = []
    
    # Process data in batches
    for batch_idx, (img, img_token, name_token, texture_name) in enumerate(tqdm(dataloader, desc="Loading batches")):
        # Average tokens across sequence length
        img_token_mean = img_token.mean(dim=1).numpy()
        name_token_mean = name_token.mean(dim=1).numpy()
        
        # Append to lists
        all_img_tokens.append(img_token_mean)
        all_name_tokens.append(name_token_mean)
        all_texture_names.extend(texture_name)
        
    # Concatenate all batches
    img_tokens = np.vstack(all_img_tokens)
    name_tokens = np.vstack(all_name_tokens)
    texture_names = all_texture_names
    
    print(f"Loaded {len(texture_names)} samples")
    
    # Get categories from texture names
    categories = [name[:3] for name in texture_names]
    unique_categories = list(set(categories))
    unique_categories.sort()
    
    print(f"Found {len(unique_categories)} unique categories: {unique_categories}")
    
    # Define markers and colors
    markers = ['o', 's', '^', 'D', 'v', '<', '>', 'p', 'h', '8']
    while len(markers) < len(unique_categories):
        markers.extend(markers)
    
    # Standardize the data
    scaler = StandardScaler()
    img_tokens = scaler.fit_transform(img_tokens)
    name_tokens = scaler.fit_transform(name_tokens)
    
    def plot_projection(data, title, filename):
        if method in ['umap', 'both']:
            print(f"\nGenerating UMAP for {title}...")
            # UMAP projection
            reducer = umap.UMAP(random_state=42)
            with tqdm(total=100, desc="UMAP projection") as pbar:
                embedding = reducer.fit_transform(data)
                pbar.update(100)
            
            plt.figure(figsize=(15, 15))
            for idx, category in tqdm(enumerate(unique_categories), 
                                    desc="Plotting categories", 
                                    total=len(unique_categories)):
                mask = [cat == category for cat in categories]
                plt.scatter(embedding[mask, 0], embedding[mask, 1],
                          marker=markers[idx], label=category, alpha=0.7)
            
            plt.title(f'UMAP - {title}')
            plt.legend()
            plt.savefig(os.path.join(output_dir, f'umap_{filename}.png'))
            plt.close()
        
        if method in ['pca', 'both']:
            print(f"\nGenerating PCA for {title}...")
            # PCA projection
            reducer = PCA(n_components=2)
            with tqdm(total=100, desc="PCA projection") as pbar:
                embedding = reducer.fit_transform(data)
                pbar.update(100)
            
            plt.figure(figsize=(15, 15))
            for idx, category in tqdm(enumerate(unique_categories), 
                                    desc="Plotting categories", 
                                    total=len(unique_categories)):
                mask = [cat == category for cat in categories]
                plt.scatter(embedding[mask, 0], embedding[mask, 1],
                          marker=markers[idx], label=category, alpha=0.7)
            
            plt.title(f'PCA - {title}')
            plt.legend()
            plt.savefig(os.path.join(output_dir, f'pca_{filename}.png'))
            plt.close()
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Plot both image and name token embeddings
    print("\nProcessing Image Tokens...")
    plot_projection(img_tokens, 'Image Tokens', 'img_tokens')
    
    print("\nProcessing Name Tokens...")
    plot_projection(name_tokens, 'Name Tokens', 'name_tokens')
    
    print(f"\nAll visualizations have been saved to: {output_dir}")


if __name__ == '__main__':
    dataset = TextureDataset('data/images_50_model_nine')
    dataloader = DataLoader(dataset, batch_size=4, shuffle=True)

    plot_embeddings(dataset, 'utils/output', method='umap')

    for img, img_token, name_token, texture_name in dataloader:
        img_mask_prob = 0.5
        img_mask = (torch.rand(img_token.size(1)) > img_mask_prob).float()
        img_mask = img_mask.unsqueeze(-1).expand_as(img_token)
        img_token = img_token * img_mask
        name_mask = (torch.rand(name_token.size(1)) > 1- img_mask_prob).float()
        name_mask = name_mask.unsqueeze(-1).expand_as(name_token)
        name_token = name_token * name_mask

        null_token = torch.zeros(1, 1, 768*2)
        target_tokens = torch.cat([null_token, img_token, name_token, null_token], dim=1)
        print(img.shape, img_token.shape, name_token.shape, target_tokens.shape, texture_name)

        # import pdb; pdb.set_trace()

        




