import os
import argparse
import yaml
from tqdm import tqdm
import torch
from src.utils import *
import numpy as np
from PIL import Image
import copy
import json

from models_mnca_internal import NoiseNCA, DyNCA
from collections import defaultdict

from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
import torch.multiprocessing as mp
from src.loss import TextureLoss, GramOmicLoss
from src.dynca_model.vector_field_loss import get_motion_vector_field_by_name

from src.dynca_model.misc.display_utils import plot_train_log
from src.motion_loss import MotionLoss


parser = argparse.ArgumentParser()
parser.add_argument('--config', type=str, default='configs/DyNCA.yml', help="Path to the config file")
parser.add_argument('--data_dir', type=str, default='data/images_8_model_nine', help="Texture images directory")
parser.add_argument('--save_dir', type=str, default='results/DyNCA/dynca_internal_vector_nine8_mask80_inje5', help="Texture images directory")
parser.add_argument('--data_batch', type=int, default=4, help="Number of images per batch")
parser.add_argument('--chn', type=int, default=16, help="Data training channel")
parser.add_argument('--pool_size', type=int, default=4, help="Pool size for each image")
parser.add_argument('--fc_dim', type=int, default=96, help="Latent dim for decoder")
parser.add_argument('--mask_prob', type=float, default=0.8, help="Mask probability")
parser.add_argument('--inject_seed_step', type=int, default=5, help="Inject seed step")
# parser.add_argument('--nca_weights', type=str, default= 'results/DyNCA/dynca_internal_nine5/final_model_circular.pth', help="NCA pre-trained model load")
# parser.add_argument('--nca_weights', type=str, default= 'results/DyNCA/dynca_internal_nine5_posno/final_model_circular.pth', help="NCA pre-trained model load")

# Vector field motion loss arguments
parser.add_argument('--motion_vector_field_name', type=str, default='grad_0_180', help="Name of the motion vector field") # grad_0_180, circular, circle, diverge, converge, hyperbolic, 2block_x, 2block_y, 3block, 4block
parser.add_argument("--motion_model_path", type=str, default='results/NCA/nca_internal_vector_ot_finetune_two/final_model.pth', help="Path to the model")
parser.add_argument("--pos_emb", type=str, default='CPE', choices=['None', 'CPE'],help="The positional embedding mode to use. CPE (Cartesian), or None")
parser.add_argument("--perception_scales", type=list, default=[0], help="Perception scales for the positional embedding")
# parser.add_argument("--perception_scales", type=list, default=[0,1,2,3,4,5], help="Perception scales for the positional embedding")
parser.add_argument("--texture_loss_weight", type=float, default=1.0, help="Weight for the texture loss")

parser.add_argument("--motion_weight_change_interval", type=int, default=500, help="Interval of iterations for changing the motion loss weight. ")

# nohup python run_dynca_internal_vector.py &
# CUDA_VISIBLE_DEVICES=1 nohup python run_dynca_internal_vector.py > output.log 2>&1 &



class TextureDataset(Dataset):
    def __init__(self, data_dir, max_size=128):
        self.image_paths = [os.path.join(data_dir, f) for f in os.listdir(data_dir) 
                          if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        self.transform = transforms.Compose([
            transforms.Resize(max_size),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
        ])
        
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        img_path = self.image_paths[idx]
        img = Image.open(img_path).convert('RGB')
        img = self.transform(img)
        texture_name = os.path.splitext(os.path.basename(img_path))[0]
        return img, texture_name


def main(config):

    device = torch.device(config['device'])
    config['loss']['attr']['device'] = device
    config['model']['attr']['device'] = device
    
    print(args.__dict__)
    args_log = copy.deepcopy(args.__dict__)
    with open(f'{config["experiment_path"]}/args.txt', 'w') as f:
        json.dump(args_log, f, indent=2)

    loss_fn = TextureLoss(**config['loss']['attr']).to(device)
    # motion loss
    motion_vector = MotionLoss(**config['model']['motion'])
    loss_log_dict = defaultdict(list)

    # dataloader
    dataset = TextureDataset(config['data_dir'])
    dataloader = DataLoader(dataset, batch_size=config['training']['batch_size'],
                          shuffle=True, num_workers=4, drop_last=False)

    nca = DyNCA(**config['model']['attr']).to(device)
    # state_dict = torch.load(args.nca_weights, map_location=device)
    # nca.load_state_dict(state_dict, strict=False)
    '''
    DyNCA(
    (pos_emb_2d): CPE2D()
    (w1): Conv2d(66, 96, kernel_size=(1, 1), stride=(1, 1))
    (w2): Conv2d(96, 16, kernel_size=(1, 1), stride=(1, 1))
    )
    '''

    opt = torch.optim.Adam(nca.parameters(), config['training']['lr'])

    if 'type' not in config['training']['scheduler'] or config['training']['scheduler']['type'] == 'MultiStep':
        lr_sched = torch.optim.lr_scheduler.MultiStepLR(opt, **config['training']['scheduler']['attr'])
    elif config['training']['scheduler']['type'] == 'Cyclic':
        lr_sched = torch.optim.lr_scheduler.CyclicLR(opt, **config['training']['scheduler']['attr'])

    iterations = config['training']['iterations']
    alpha = config['training']['overflow_weight']
    step_range = config['training']['nca']['step_range']
    inject_seed_step = config['training']['nca']['inject_seed_step']
    pool_size = config['training']['nca']['pool_size']

    # output dir
    texture_names = [os.path.splitext(f)[0] for f in os.listdir(config['data_dir'])
                   if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
    output_dirs = {}
    for name in texture_names:
        output_dir = os.path.join(config['experiment_path'],  name)
        os.makedirs(output_dir, exist_ok=True)
        output_dirs[name] = output_dir
    
    replacer = RandomPatchReplacer(min_percent=0.1, max_percent=args.mask_prob)

    pools = {}
    for idx, (_, texture_name) in enumerate(dataset):
        pools[texture_name] = nca.seed(pool_size)
        pools[texture_name] = pool_genome(texture_name, pools)


    # for epoch in tqdm(range(iterations)):
    for epoch in range(iterations):
        for idx, (target_images, texture_names) in enumerate(dataloader):
            target_images = target_images.to(device)
            
            batch_x = []
            batch_indices = []
            
            for i, texture_name in enumerate(texture_names):
                pool = pools[texture_name]
                pool_idx = np.random.choice(pool_size, 1, replace=False) 
                x = pool[pool_idx] 
                
                new = nca.seed(1)
                new_x = one_genome(texture_name, new)
                x = replacer.apply(x, new_x)
                if epoch % inject_seed_step == 0:
                    x = new_x

                batch_x.append(x)
                batch_indices.append((texture_name, pool_idx))
            
            x = torch.cat(batch_x, dim=0)
            image_before_nca = nca.to_rgb(x)
            
            step_n = np.random.randint(step_range[0], step_range[1])
            # x, image_before_nca = nca.forward_nsteps(x, step_n, update_rate=0.5)
            x, image_after_nca = nca.forward_nsteps(x, step_n, update_rate=0.5)

            # Calculate losses
            overflow_loss = (x - x.clamp(-5.0, 5.0)).abs().sum()
            
            texture_loss = loss_fn(target_images, image_after_nca)[0]

            motion_loss, direction_loss_value, strength_loss_value = motion_vector.compute_loss(
                image_before_nca, image_after_nca,
                nca_num_steps=step_n,
                return_summary=True
            )

            loss_log_dict['overflow'].append(overflow_loss.item())
            loss_log_dict['appearance'].append(texture_loss.item())
            loss_log_dict['vector_field_motion'].append(motion_loss.item())
            loss_log_dict['vector_field_motion-direction'].append(direction_loss_value)
            loss_log_dict['vector_field_motion-strength'].append(strength_loss_value)
            if epoch % args.motion_weight_change_interval == 0 and epoch > 0:
                new_weight = motion_vector.set_motion_weight(appearance_loss_log=loss_log_dict['appearance'])
                # args.texture_loss_weight = 10.0

            loss = args.texture_loss_weight * texture_loss + overflow_loss + motion_loss
            print(f'epoch: {epoch}, texture_loss: {texture_loss.item():.4f}, overflow: {overflow_loss.item():.4f}, motion_loss: {motion_loss.item():.4f}')


            opt.zero_grad()
            loss.backward()
            for p in nca.parameters():
                p.grad /= (p.grad.norm() + 1e-8)
            opt.step()
            lr_sched.step()
            
            with torch.no_grad():
                for i, (texture_name, pool_idx) in enumerate(batch_indices):
                    pools[texture_name][pool_idx] = x[i:i+1].detach()


            if (epoch % config['training']['log_interval'] == 0) or (epoch == iterations - 1):
                original_imgs = ((target_images.permute(0, 2, 3, 1).detach().cpu().numpy()/2+0.5) * 255.0).astype(np.uint8)
                for idx, (texture_name, original_img) in enumerate(zip( texture_names, original_imgs)):
                    generated_states = pools[texture_name][:args.pool_size]
                    imgs = nca.to_rgb(generated_states).permute([0, 2, 3, 1]).detach().cpu().numpy()
                    imgs = ((np.clip(imgs, -1, 1)+1)/2.0 * 255.0).astype(np.uint8)
                    imgs = np.hstack([original_img] + [imgs[i] for i in range(args.pool_size)])

                    Image.fromarray(imgs).save(f'{output_dirs[texture_name]}/{texture_name}-{args.motion_vector_field_name}-epoch-{epoch}.png')

                    # Save vector field visualizations
                    vector_field_dir = os.path.join(config['experiment_path'], texture_name, args.motion_vector_field_name)
                    motion_vector.save_visualizations(vector_field_dir, epoch=epoch)

                    plot_log_dict = {}
                    plot_log_dict['Overflow Loss'] = (loss_log_dict['overflow'], True, True)
                    plot_log_dict['Texture Loss'] = (loss_log_dict['appearance'], True, True)
                    plot_train_log(plot_log_dict, 2, save_path=f"{vector_field_dir}/losses.jpg")

                    plot_log_dict = {}
                    plot_log_dict['Motion Loss'] = (loss_log_dict['vector_field_motion'], False, False)
                    plot_log_dict['Motion Direction Loss'] = (loss_log_dict['vector_field_motion-direction'], False, False)
                    plot_log_dict['Motion Strength Loss'] = (loss_log_dict['vector_field_motion-strength'], False, False)
                    plot_train_log(plot_log_dict, 5, save_path=f"{vector_field_dir}/losses_motion.jpg")

    
        if (epoch % config['training']['log_interval'] == 0) or (epoch == iterations - 1):
            torch.save(nca.state_dict(), os.path.join(config['experiment_path'], f"final_model_{args.motion_vector_field_name}.pth"))
            pool_names = list(pools.keys())
            for texture_name in pool_names:
                # final_state = pools[texture_name][:1]
                final_state = nca.seed(1)
                final_state = one_genome(texture_name, final_state)
                save_video(state=final_state, output_dir=f"{config['experiment_path']}/{texture_name}", video_name=f"{args.motion_vector_field_name}", nca_model=nca, video_length=3.0, step_n=config['model']['motion']['nca_base_num_steps'])

                large_size = 256  # 2倍大小
                large_state = nca.seed(1, h=large_size, w=large_size)
                large_state = one_genome(texture_name, large_state)

                save_video(state=large_state, output_dir=f"{config['experiment_path']}/{texture_name}", video_name=f"{args.motion_vector_field_name}_large", nca_model=nca, video_length=3.0, step_n=config['model']['motion']['nca_base_num_steps'])
                

if __name__ == "__main__":
    args = parser.parse_args()
    with open(args.config, 'r') as stream:
        config = yaml.load(stream, Loader=yaml.FullLoader)

    config['training']['lr'] = 0.001
    config['model']['attr']['c_in'] = args.chn
    config['model']['attr']['fc_dim'] = args.fc_dim
    config['data_dir'] = args.data_dir
    config['training']['batch_size'] = args.data_batch
    config['training']['nca']['pool_size'] = args.pool_size
    config['device'] = 'cuda' if torch.cuda.is_available() else 'cpu'

    config['model']['motion']['motion_vector_field_name'] = args.motion_vector_field_name
    config['model']['attr']['pos_emb'] = args.pos_emb
    config['model']['attr']['perception_scales'] = args.perception_scales
    config['training']['nca']['inject_seed_step'] = args.inject_seed_step

    exp_name = config['experiment_name']
    exp_path = args.save_dir
    config['experiment_path'] = exp_path
    if not os.path.exists(exp_path):
        os.makedirs(exp_path)
    main(config)
