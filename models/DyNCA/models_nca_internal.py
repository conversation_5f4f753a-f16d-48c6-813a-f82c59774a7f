import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.distributions import Normal, Distribution
import numpy as np

def depthwise_conv(x, filters, padding='circular'):
    """filters: [filter_n, h, w]"""
    b, ch, h, w = x.shape
    y = x.reshape(b * ch, 1, h, w)
    y = torch.nn.functional.pad(y, [1, 1, 1, 1], padding)
    y = torch.nn.functional.conv2d(y, filters[:, None])
    return y.reshape(b, -1, h, w)


def merge_lap(z):
    # This function merges the lap_x and lap_y into a single laplacian filter
    b, c, h, w = z.shape
    z = torch.stack([
        z[:, ::5],
        z[:, 1::5],
        z[:, 2::5],
        z[:, 3::5] + z[:, 4::5]
    ],
        dim=2)  # [b, chn, 4, h, w]
    return z.reshape(b, -1, h, w)  # [b, 4 * chn, h, w]

# ori
class NoiseNCA(torch.nn.Module):
    """
    Base class for Neural Cellular Automata.
    The functionalities to change the scale of perception filters and to add conditional channels are included in
    the base class. The extensions such as NoiseNCA and PENCA are implemented by inheriting from this class.
    """

    def __init__(self, chn, fc_dim,
                 padding='circular', perception_kernels=4,
                 cond_chn=0, update_prob=0.5, device=None):
        """
        chn: Number of channels in the cell state
        fc_dim: Number of channels in the update MLP hidden layer
        padding: Padding mode for the perception (Convolution kernels)
        perception_kernels: Number of perception kernels. The baseline NCA uses 4 kernels: Identity, Sobel X, Sobel Y, Laplacian
        cond_chn: Number of conditional channels.
                  For example a 2D positional encoding will add 2 extra condition channels. If the number of conditional
                  channels is > 0 then you need to override the adaptation/perception methods and provide the extra condition channels.
        update_prob: Probability of updating a cell state in each iteration.
                     If update_prob = 1.0, all the cells are updated in each iteration.
        device: PyTorch device
        """
        super(NoiseNCA, self).__init__()
        self.chn, self.fc_dim, self.padding, self.perception_kernels = chn, fc_dim, padding, perception_kernels
        self.cond_chn, self.update_prob, self.device = cond_chn, update_prob, device

        self.w1 = torch.nn.Conv2d(chn * perception_kernels + cond_chn, fc_dim, 1, bias=True, device=device) # bias None in dynca
        self.w2 = torch.nn.Conv2d(fc_dim, chn, 1, bias=False, device=device)  # bias True in dynca
        torch.nn.init.xavier_normal_(self.w1.weight, gain=0.2)
        torch.nn.init.zeros_(self.w2.weight) # xavier init with gain=0.1 + bias=0 in dynca

        with torch.no_grad():
            ident = torch.tensor([[0.0, 0.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 0.0]], device=device)
            sobel_x = torch.tensor([[-1.0, 0.0, 1.0], [-2.0, 0.0, 2.0], [-1.0, 0.0, 1.0]], device=device)
            lap_x = torch.tensor([[0.5, 0.0, 0.5], [2.0, -6.0, 2.0], [0.5, 0.0, 0.5]], device=device)

            self.filters = torch.stack([ident, sobel_x, sobel_x.T, lap_x, lap_x.T])

        self.p_z = Normal(torch.zeros(self.chn, device=self.device),
                          torch.ones(self.chn, device=self.device))
        
        self.pos_emb_2d = CPE2D()

    def perception(self, s, dx=1.0, dy=1.0):
        """
        Computes the perception vector for each cell given the current cell states s.
        dx, dy are used to scale the sobel and laplacian filters.
        dx, dy < 1.0 means that the patterns are gonna get stretched horizontally, vertically.
        dx, dy > 1.0 means that the patterns are gonna get squeezed horizontally, vertically.
        """

        z = depthwise_conv(s, self.filters, self.padding)  # [b, 5 * chn, h, w]
        if dx == 1.0 and dy == 1.0:
            return merge_lap(z)

        if not isinstance(dx, torch.Tensor) or dx.ndim != 3:
            dx = torch.tensor([dx], device=s.device)[:, None, None]  # [1, 1, 1]
        if not isinstance(dy, torch.Tensor) or dy.ndim != 3:
            dy = torch.tensor([dy], device=s.device)[:, None, None]  # [1, 1, 1]

        scale = 1.0 / torch.stack([torch.ones_like(dx), dx, dy, dx ** 2, dy ** 2], dim=1)
        scale = torch.tile(scale, (1, self.chn, 1, 1))
        z = z * scale
        return merge_lap(z)

    def adaptation(self, s, dx=1.0, dy=1.0):
        """Computes the residual update given current cell states s"""
        z = self.perception(s, dx, dy)
        z[:, 3:5,:,:] = self.pos_emb_2d(s)
        delta_s = self.w2(torch.relu(self.w1(z)))
        return delta_s

    def step_euler(self, s, dx=1.0, dy=1.0, dt=1.0):
        """Computes one step of the NCA update using the Euler integrator."""
        delta_s = self.adaptation(s, dx, dy)
        M = 1.0
        if self.update_prob < 1.0:
            b, _, h, w = s.shape
            M = (torch.rand(b, 1, h, w, device=s.device) + self.update_prob).floor()

        return s + delta_s * M * dt

    def step_rk4(self, s, dx=1.0, dy=1.0, dt=1.0):
        """Computes one step of the NCA update using the 4th order Runge-Kutta integrator."""
        M = 1.0
        if self.update_prob < 1.0:
            b, _, h, w = s.shape
            M = (torch.rand(b, 1, h, w, device=s.device) + self.update_prob).floor()

        k1 = self.adaptation(s, dx, dy)
        k2 = self.adaptation(s + k1 * 0.5 * M, dx, dy)
        k3 = self.adaptation(s + k2 * 0.5 * M, dx, dy)
        k4 = self.adaptation(s + k3 * M, dx, dy)
        return s + (k1 + 2 * k2 + 2 * k3 + k4) * dt * M / 6.0

    def forward(self, s, dx=1.0, dy=1.0, dt=1.0, integrator='euler'):
        """
        Computes one step of the NCA update rule using the specified integrator.

        :param s: Cell states tensor of shape [b, chn, h, w]
        :param dx: Either a float or a tensor of shape [b, h, w]
        :param dy: Either a float or a tensor of shape [b, h, w]
        :param dt: Time step used for integration. Must be a float value <= 1.0
        :param integrator: Integration method. Either 'euler' or 'rk4'
        """
        if integrator == 'euler':
            return self.step_euler(s, dx, dy, dt)
        elif integrator == 'rk4':
            return self.step_rk4(s, dx, dy, dt)
        else:
            raise ValueError("Invalid integrator. Must be either 'euler' or 'rk4'")

    # def seed(self, n, h=128, w=128):
    #     """Starting cell state"""
    #     return torch.zeros(n, self.chn, h, w, device=self.device)

    def seed(self, n, h=128, w=128):
        z = self.p_z.sample((n, h, w)).reshape(n, self.chn, h, w) * 0.25
        return z


    def to_rgb(self, s):
        """Converts the cell state to RGB. The offset of 0.5 is added so that the valid values are in [0, 1] range."""
        # return s[..., :3, :, :] + 0.5
        return s[:, :3, :, :]  * 0.25

    def to(self, *args, **kwargs):
        super().to(*args, **kwargs)
        self.filters = self.filters.to(*args, **kwargs)
        self.device = self.w1.weight.device
        return self


# with 2d postition
# class NoiseNCA(torch.nn.Module):
#     """
#     Base class for Neural Cellular Automata.
#     The functionalities to change the scale of perception filters and to add conditional channels are included in
#     the base class. The extensions such as NoiseNCA and PENCA are implemented by inheriting from this class.
#     """

#     def __init__(self, chn, fc_dim,
#                  padding='circular', perception_kernels=4,
#                  cond_chn=2, update_prob=0.5, device=None):
#         """
#         chn: Number of channels in the cell state
#         fc_dim: Number of channels in the update MLP hidden layer
#         padding: Padding mode for the perception (Convolution kernels)
#         perception_kernels: Number of perception kernels. The baseline NCA uses 4 kernels: Identity, Sobel X, Sobel Y, Laplacian
#         cond_chn: Number of conditional channels.
#                   For example a 2D positional encoding will add 2 extra condition channels. If the number of conditional
#                   channels is > 0 then you need to override the adaptation/perception methods and provide the extra condition channels.
#         update_prob: Probability of updating a cell state in each iteration.
#                      If update_prob = 1.0, all the cells are updated in each iteration.
#         device: PyTorch device
#         """
#         super(NoiseNCA, self).__init__()
#         self.chn, self.fc_dim, self.padding, self.perception_kernels = chn, fc_dim, padding, perception_kernels
#         self.cond_chn, self.update_prob, self.device = cond_chn, update_prob, device

#         self.w1 = torch.nn.Conv2d(chn * perception_kernels + cond_chn, fc_dim, 1, bias=True, device=device) # bias None in dynca
#         self.w2 = torch.nn.Conv2d(fc_dim, chn, 1, bias=False, device=device)  # bias True in dynca
#         torch.nn.init.xavier_normal_(self.w1.weight, gain=0.2)
#         torch.nn.init.zeros_(self.w2.weight) # xavier init with gain=0.1 + bias=0 in dynca

#         with torch.no_grad():
#             ident = torch.tensor([[0.0, 0.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 0.0]], device=device)
#             sobel_x = torch.tensor([[-1.0, 0.0, 1.0], [-2.0, 0.0, 2.0], [-1.0, 0.0, 1.0]], device=device)
#             lap_x = torch.tensor([[0.5, 0.0, 0.5], [2.0, -6.0, 2.0], [0.5, 0.0, 0.5]], device=device)

#             self.filters = torch.stack([ident, sobel_x, sobel_x.T, lap_x, lap_x.T])

#         self.p_z = Normal(torch.zeros(self.chn, device=self.device),
#                           torch.ones(self.chn, device=self.device))
        
#         self.pos_emb_2d = CPE2D()

#     def perception(self, s, dx=1.0, dy=1.0):
#         """
#         Computes the perception vector for each cell given the current cell states s.
#         dx, dy are used to scale the sobel and laplacian filters.
#         dx, dy < 1.0 means that the patterns are gonna get stretched horizontally, vertically.
#         dx, dy > 1.0 means that the patterns are gonna get squeezed horizontally, vertically.
#         """

#         z = depthwise_conv(s, self.filters, self.padding)  # [b, 5 * chn, h, w]
#         if dx == 1.0 and dy == 1.0:
#             return merge_lap(z)

#         if not isinstance(dx, torch.Tensor) or dx.ndim != 3:
#             dx = torch.tensor([dx], device=s.device)[:, None, None]  # [1, 1, 1]
#         if not isinstance(dy, torch.Tensor) or dy.ndim != 3:
#             dy = torch.tensor([dy], device=s.device)[:, None, None]  # [1, 1, 1]

#         scale = 1.0 / torch.stack([torch.ones_like(dx), dx, dy, dx ** 2, dy ** 2], dim=1)
#         scale = torch.tile(scale, (1, self.chn, 1, 1))
#         z = z * scale
#         return merge_lap(z)

#     def adaptation(self, s, dx=1.0, dy=1.0):
#         """Computes the residual update given current cell states s"""
#         z = self.perception(s, dx, dy)
#         z = torch.cat([z, self.pos_emb_2d(s)], dim=1)
#         delta_s = self.w2(torch.relu(self.w1(z)))
#         return delta_s

#     def step_euler(self, s, dx=1.0, dy=1.0, dt=1.0):
#         """Computes one step of the NCA update using the Euler integrator."""
#         delta_s = self.adaptation(s, dx, dy)
#         M = 1.0
#         if self.update_prob < 1.0:
#             b, _, h, w = s.shape
#             M = (torch.rand(b, 1, h, w, device=s.device) + self.update_prob).floor()

#         return s + delta_s * M * dt

#     def step_rk4(self, s, dx=1.0, dy=1.0, dt=1.0):
#         """Computes one step of the NCA update using the 4th order Runge-Kutta integrator."""
#         M = 1.0
#         if self.update_prob < 1.0:
#             b, _, h, w = s.shape
#             M = (torch.rand(b, 1, h, w, device=s.device) + self.update_prob).floor()

#         k1 = self.adaptation(s, dx, dy)
#         k2 = self.adaptation(s + k1 * 0.5 * M, dx, dy)
#         k3 = self.adaptation(s + k2 * 0.5 * M, dx, dy)
#         k4 = self.adaptation(s + k3 * M, dx, dy)
#         return s + (k1 + 2 * k2 + 2 * k3 + k4) * dt * M / 6.0

#     def forward(self, s, dx=1.0, dy=1.0, dt=1.0, integrator='euler'):
#         """
#         Computes one step of the NCA update rule using the specified integrator.

#         :param s: Cell states tensor of shape [b, chn, h, w]
#         :param dx: Either a float or a tensor of shape [b, h, w]
#         :param dy: Either a float or a tensor of shape [b, h, w]
#         :param dt: Time step used for integration. Must be a float value <= 1.0
#         :param integrator: Integration method. Either 'euler' or 'rk4'
#         """
#         if integrator == 'euler':
#             return self.step_euler(s, dx, dy, dt)
#         elif integrator == 'rk4':
#             return self.step_rk4(s, dx, dy, dt)
#         else:
#             raise ValueError("Invalid integrator. Must be either 'euler' or 'rk4'")

#     # def seed(self, n, h=128, w=128):
#     #     """Starting cell state"""
#     #     return torch.zeros(n, self.chn, h, w, device=self.device)

#     def seed(self, n, h=128, w=128):
#         z = self.p_z.sample((n, h, w)).reshape(n, self.chn, h, w) * 0.25
#         return z


#     def to_rgb(self, s):
#         """Converts the cell state to RGB. The offset of 0.5 is added so that the valid values are in [0, 1] range."""
#         # return s[..., :3, :, :] + 0.5
#         return s[:, :3, :, :]  * 0.25

#     def to(self, *args, **kwargs):
#         super().to(*args, **kwargs)
#         self.filters = self.filters.to(*args, **kwargs)
#         self.device = self.w1.weight.device
#         return self




class CPE2D(nn.Module):
    """
    Cartesian Positional Encoding 2D
    """

    def __init__(self):
        super(CPE2D, self).__init__()
        self.cached_penc = None
        self.last_tensor_shape = None

    def forward(self, tensor):
        """
        :param tensor: A tensor of size (batch_size, ch, x, y)
        :return: Positional Encoding Matrix of size (batch_size, 2, x, y)
        """
        if len(tensor.shape) != 4:
            if isinstance(tensor, tuple) and len(tensor) == 2:
                tensor = tensor[0]

            if len(tensor.shape) == 3:
                tensor = tensor.unsqueeze(0)  # 添加批量维度
            elif len(tensor.shape) == 2:
                tensor = tensor.unsqueeze(0).unsqueeze(0)  # 添加批量和通道维度

            if len(tensor.shape) != 4:
                raise RuntimeError(f"Cannot convert tensor of shape {tensor.shape} to 4D!")

        if self.cached_penc is not None and self.last_tensor_shape == tensor.shape:
            return self.cached_penc

        self.cached_penc = None
        batch_size, orig_ch, h, w = tensor.shape
        xs = torch.arange(h, device=tensor.device) / h # [0,1,2,3,4]/5 -> [0,1)
        ys = torch.arange(w, device=tensor.device) / w
        xs = 2.0 * (xs - 0.5 + 0.5 / h) # -> [-1, 1]
        ys = 2.0 * (ys - 0.5 + 0.5 / w)
        xs = xs[None, :, None]
        ys = ys[None, None, :]
        emb = torch.zeros((2, h, w), device=tensor.device).type(tensor.type())
        emb[:1] = xs
        emb[1: 2] = ys

        self.cached_penc = emb.unsqueeze(0).repeat(batch_size, 1, 1, 1) # -> [b, 2, h, w]
        self.last_tensor_shape = tensor.shape

        return self.cached_penc


class DyNCA(torch.nn.Module):
    """
    Parameters
    ----------
    c_in: int, required
        Number of channels in the input
        Note that each channel will be processed
        using 3, or 4 (if laplacian=True) convolution filters
    c_out: int, required
        Number of channels in the output
        Note that the NCA will be performed using c_in channels
        and the output of the NCA will be expanded to c_out
        channels using a learnable 1x1 convolution layer
    fc_dim: int, default=94
        Number of channels in the intermediate fully connected layer
    random_seed: int, default=None
    seed_mode: {'zeros', 'center_on', 'random'}, default='constant'
        Type of the seed used to initialize the cellular automata
    device: pytorch device
        Device used for performing the computation.
    """

    def __init__(self, c_in, c_out, fc_dim=96,
                 padding_mode='replicate',
                 pos_emb='CPE',
                 perception_scales=[0],
                 device=torch.device("cuda:0")):

        super().__init__()
        self.c_in = c_in
        self.c_out = c_out
        self.perception_scales = perception_scales
        self.fc_dim = fc_dim
        self.padding_mode = padding_mode
        self.random_seed = 42
        self.pos_emb = pos_emb
        self.device = device
        self.expand = 4

        self.c_cond = 0
        if self.pos_emb == 'CPE':
            self.pos_emb_2d = CPE2D()
            self.c_cond += 2
        else:
            self.pos_emb_2d = None

        self.w1 = torch.nn.Conv2d(self.c_in * self.expand + self.c_cond, self.fc_dim, 1, device=self.device)
        torch.nn.init.xavier_normal_(self.w1.weight, gain=0.2)

        self.w2 = torch.nn.Conv2d(self.fc_dim, self.c_in, 1, bias=True, device=self.device)
        torch.nn.init.xavier_normal_(self.w2.weight, gain=0.1)
        torch.nn.init.zeros_(self.w2.bias)

        self.sobel_filter_x = torch.FloatTensor([[-1.0, 0.0, 1.0], [-2.0, 0.0, 2.0], [-1.0, 0.0, 1.0]]).to(
            self.device)
        self.sobel_filter_y = self.sobel_filter_x.T

        self.identity_filter = torch.FloatTensor([[0, 0, 0], [0, 1, 0], [0, 0, 0]]).to(self.device)
        self.laplacian_filter = torch.FloatTensor([[1.0, 2.0, 1.0], [2.0, -12, 2.0], [1.0, 2.0, 1.0]]).to(self.device)

        self.p_z = Normal(torch.zeros(self.c_in, device=self.device),
                          torch.ones(self.c_in, device=self.device))

    def perceive_torch(self, x, scale=0):
        assert scale in [0, 1, 2, 3, 4, 5] 
        if scale != 0:
            _, _, h, w = x.shape
            h_new = int(h // (2 ** scale))
            w_new = int(w // (2 ** scale))
            x = F.interpolate(x, size=(h_new, w_new), mode='bilinear', align_corners=False) # 1/(2^scale)

        def _perceive_with_torch(z, weight):
            conv_weights = weight.reshape(1, 1, 3, 3).repeat(self.c_in, 1, 1, 1) # [c_in, 1, 3, 3]
            z = F.pad(z, [1, 1, 1, 1], self.padding_mode)
            return F.conv2d(z, conv_weights, groups=self.c_in)

        y1 = _perceive_with_torch(x, self.sobel_filter_x)
        y2 = _perceive_with_torch(x, self.sobel_filter_y)
        y3 = _perceive_with_torch(x, self.laplacian_filter)

        tensor_list = [x]
        tensor_list += [y1, y2, y3]

        y = torch.cat(tensor_list, dim=1)

        if scale != 0:
            y = F.interpolate(y, size=(h, w), mode='bilinear', align_corners=False)

        return y

    def perceive_multiscale(self, x, pos_emb_mat=None):
        perceptions = []
        y = 0
        for scale in self.perception_scales:
            z = self.perceive_torch(x, scale=scale)
            perceptions.append(z)
        y = sum(perceptions)
        y = y / len(self.perception_scales)

        if pos_emb_mat is not None:
            y = torch.cat([y, pos_emb_mat], dim=1)

        return y

    def to_rgb(self, x):
        return x[:, :self.c_out, ...] * 0.25

    def seed(self, n, h=128, w=128):
        z = self.p_z.sample((n, h, w)).reshape(n, self.c_in, h, w) * 0.25
        return z

    def forward_nsteps(self, input_state, step_n, update_rate=0.5, return_middle_feature=False):
        nca_state = input_state
        middle_feature_list = []
        for _ in range(step_n):
            nca_state, nca_feature = self(nca_state, update_rate=update_rate)
            if return_middle_feature:
                middle_feature_list.append(nca_feature)
        if return_middle_feature:
            return nca_state, nca_feature, middle_feature_list
        return nca_state, nca_feature
    
    def forward(self, x, update_rate=0.5, return_perception=False):
        if self.pos_emb_2d:
            y_percept = self.perceive_multiscale(x, pos_emb_mat=self.pos_emb_2d(x))
        else:
            y_percept = self.perceive_multiscale(x)
        y = self.w2(F.relu(self.w1(y_percept)))
        b, c, h, w = y.shape

        update_mask = (torch.rand(b, 1, h, w, device=self.device) + update_rate).floor()
        # import pdb; pdb.set_trace()

        x = x + y * update_mask

        if return_perception:
            return x, self.to_rgb(x), y_percept
        else:
            return x, self.to_rgb(x)


if __name__ == "__main__":
    device = torch.device("cuda")
    # 使用DyNCA替代NoiseNCA
    model = DyNCA(c_in=12, c_out=3, fc_dim=96, device=device)

    state_dict = torch.load("weights.pt", map_location="cpu")
    model.load_state_dict(state_dict)

    from tqdm import tqdm
    from utils.video_utils import VideoWriter

    with VideoWriter() as vid, torch.no_grad():
        s = model.seed(1, 512, 512).to(device)
        for k in tqdm(range(600)):
            step_n = 8
            for i in range(step_n):
                s, _ = model(s)

            img = model.to_rgb(s[0]).permute(1, 2, 0).cpu()
            vid.add(img)