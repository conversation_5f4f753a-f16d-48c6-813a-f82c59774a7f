import os
import argparse
import yaml
from tqdm import tqdm
import torch
from src.utils import *
import numpy as np
from PIL import Image
import copy
import json

from models_nca_internal import NoiseNCA, DyNCA
from collections import defaultdict

from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
import torch.multiprocessing as mp
from src.loss import TextureLoss, GramOmicLoss
from src.dynca_model.vector_field_loss import get_motion_vector_field_by_name

from src.dynca_model.misc.display_utils import plot_train_log
from src.video_loss import VideoMotionLoss
# from src.dynca_model.video_motion_loss import VideoMotionLoss
from src.dynca_model.misc.preprocess_texture import preprocess_video, get_train_image_seq
from src.dynca_model.misc.misc import get_start_frame_idx
from src.dynca_model.video_utills import synthesize_video


parser = argparse.ArgumentParser()
parser.add_argument('--config', type=str, default='configs/DyNCA.yml', help="Path to the config file")
parser.add_argument('--data_dir', type=str, default='data_test_two', help="Texture images directory")
parser.add_argument('--save_dir', type=str, default='results/DyNCA/dynca_internal_video_two', help="Texture images directory")
parser.add_argument('--data_batch', type=int, default=2, help="Number of images per batch")
parser.add_argument('--chn', type=int, default=16, help="Data training channel")
parser.add_argument('--pool_size', type=int, default=4, help="Pool size for each image")
parser.add_argument('--fc_dim', type=int, default=96, help="Latent dim for decoder")
parser.add_argument('--mask_prob', type=float, default=0.3, help="Mask probability")
parser.add_argument('--nca_weights', type=str, default= 'results/DyNCA/dynca_internal_nine5/final_model_circular.pth', help="NCA pre-trained model load")
# parser.add_argument('--nca_weights', type=str, default= 'results/DyNCA/dynca_internal_nine5_posno/final_model_circular.pth', help="NCA pre-trained model load")

# Vector field motion loss arguments
# parser.add_argument("--motion_model_path", type=str, default='results/NCA/nca_internal_vector_ot_finetune_two/final_model.pth', help="Path to the model")
# parser.add_argument("--pos_emb", type=str, default='CPE', choices=['None', 'CPE'],help="The positional embedding mode to use. CPE (Cartesian), or None")
# parser.add_argument("--perception_scales", type=list, default=[0], help="Perception scales for the positional embedding")
parser.add_argument("--perception_scales", type=list, default=[0,1], help="Perception scales for the positional embedding")
# parser.add_argument("--motion_weight_change_interval", type=int, default=500, help="Interval of iterations for changing the motion loss weight. ") #500

# video loss arguments
# parser.add_argument("--target_dynamics_path", type=str, default='data/VideoMotion/Appearance/smoke_plume_1.gif', help="Path to style video")
# parser.add_argument("--target_appearance_path", type=str, default='data/VideoMotion/Appearance/smoke_plume_1.gif', help="Path to style image")
parser.add_argument("--target_dynamics_path", type=str, default='data/VideoMotion/Motion/water_3.gif', help="Path to style video")
parser.add_argument("--motion_nca_interval", type=int, default=64, help="Number of NCA steps that represents motion between two consecutive frames.")
parser.add_argument("--nca_warmup_iter", type=int, default=1000, help="Number of iterations for DyNCA to warm up to set motion weight") #1000
parser.add_argument("--video_length", type=int, default=3, help="Length of the generated video in seconds")
parser.add_argument("--video_motion_loss_weight", type=float, default=1.0, help="Weight for the video motion loss")

# CUDA_VISIBLE_DEVICES=1 python run_dynca_internal_video.py
# nohup python run_dynca_internal_video.py &
# CUDA_VISIBLE_DEVICES=3 nohup python run_dynca_internal_video.py > output.log 2>&1 &

'''
texture
input_dict['target_image_list']
input_dict['generated_image_list']

video
input_dict['target_motion_image_list']
input_dict['generated_image_list_motion']
'''


class TextureDataset(Dataset):
    def __init__(self, data_dir, max_size=128):
        self.image_paths = [os.path.join(data_dir, f) for f in os.listdir(data_dir) 
                          if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        self.transform = transforms.Compose([
            transforms.Resize(max_size),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
        ])
        
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        img_path = self.image_paths[idx]
        img = Image.open(img_path).convert('RGB')
        img = self.transform(img)
        texture_name = os.path.splitext(os.path.basename(img_path))[0]
        return img, texture_name


def main(config):

    device = torch.device(config['device'])
    config['loss']['attr']['device'] = device
    config['model']['attr']['device'] = device
    
    print(args.__dict__)
    args_log = copy.deepcopy(args.__dict__)
    with open(f'{config["experiment_path"]}/args.txt', 'w') as f:
        json.dump(args_log, f, indent=2)

    # loss
    texture_loss_fn = TextureLoss(**config['loss']['attr']).to(device)
    video_motion_loss_fn = VideoMotionLoss(**config['model']['motion_video'])
    video_motion_loss_fn.set_video_weight(medium_mt=5.0)
    loss_log_dict = defaultdict(list)

    # data
    target_image_seq = video_motion_loss_fn.preprocess_video(args.target_dynamics_path).permute(1, 0, 2, 3).to(device)
    dataset = TextureDataset(config['data_dir'])
    dataloader = DataLoader(dataset, batch_size=config['training']['batch_size'],
                          shuffle=True, num_workers=4, drop_last=False)

    # model
    nca = DyNCA(**config['model']['attr']).to(device)
    # state_dict = torch.load(args.nca_weights, map_location=device)
    # nca.load_state_dict(state_dict, strict=False)
    param_n = sum(p.numel() for p in nca.parameters())
    print('NCA param count:', param_n)

    opt = torch.optim.Adam(nca.parameters(), config['training']['lr'])
    if 'type' not in config['training']['scheduler'] or config['training']['scheduler']['type'] == 'MultiStep':
        lr_sched = torch.optim.lr_scheduler.MultiStepLR(opt, **config['training']['scheduler']['attr'])
    elif config['training']['scheduler']['type'] == 'Cyclic':
        lr_sched = torch.optim.lr_scheduler.CyclicLR(opt, **config['training']['scheduler']['attr'])

    iterations = config['training']['iterations']
    step_range = config['training']['nca']['step_range']
    inject_seed_step = config['training']['nca']['inject_seed_step']
    pool_size = config['training']['nca']['pool_size']

    # output dir
    texture_names = [os.path.splitext(f)[0] for f in os.listdir(config['data_dir'])
                   if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
    output_dirs = {}
    for name in texture_names:
        output_dir = os.path.join(config['experiment_path'],  name)
        os.makedirs(output_dir, exist_ok=True)
        output_dirs[name] = output_dir
    
    replacer = RandomPatchReplacer(min_percent=0.1, max_percent=args.mask_prob)
    pools = {}
    image_seq_texture = {}
    frame_idx_texture = {}
    for idx, (_, texture_name) in enumerate(dataset):
        pools[texture_name] = nca.seed(pool_size)
        pools[texture_name] = pool_genome(texture_name, pools)
        # target_image_seq_texture, target_image_texture, target_image_texture_save, target_frame_idx_texture = video_motion_loss_fn.get_train_image_seq(f'{config["data_dir"]}/{texture_name}.jpg', args.target_dynamics_path)
        # image_seq_texture[texture_name] = target_image_seq_texture
        # frame_idx_texture[texture_name] = target_frame_idx_texture
        # target_image_texture_save.save(f"{args.save_dir}/{texture_name}/select_frame.png")
        # print(f"target_image_seq_texture shape: {target_image_seq_texture.shape}")
        # print(f"Select {target_frame_idx_texture} frame for {texture_name}")
        # import pdb; pdb.set_trace()

    for epoch in tqdm(range(iterations)):
        # np.random.seed(epoch + 424)
        # torch.manual_seed(epoch + 424)
        # torch.cuda.manual_seed_all(epoch + 424)
        for idx, (target_images, texture_names) in enumerate(dataloader):
            target_images = target_images.to(device)
            # import pdb; pdb.set_trace()

            batch_x = []
            batch_indices = []
            
            for i, texture_name in enumerate(texture_names):
                pool = pools[texture_name]
                pool_idx = np.random.choice(pool_size, 1, replace=False) 
                x = pool[pool_idx] 
                
                new = nca.seed(1)
                new_x = one_genome(texture_name, new)
                x = replacer.apply(x, new_x)
                seed_injection = False
                if epoch % inject_seed_step == 0:
                    seed_injection = True
                    x = new_x

                batch_x.append(x)
                batch_indices.append((texture_name, pool_idx))
            
            x = torch.cat(batch_x, dim=0)
            step_n = np.random.randint(step_range[0], step_range[1])
            x, image_after_nca, middle_feature_list = nca.forward_nsteps(x, step_n, update_rate=0.5, return_middle_feature=True)
            
            # generated_image_list_motion
            nca_video_feature_list = middle_feature_list[::args.motion_nca_interval]
            generated_image_list_motion = (nca_video_feature_list.copy() if seed_injection 
                                                else nca_video_feature_list)

            # target_image_list_motion
            image_list_length = len(generated_image_list_motion)
            idx_vid = get_start_frame_idx(target_image_seq.shape[0], image_list_length)
            print(f"idx_vid: {idx_vid}", 'video length: ', target_image_seq.shape[0], 'image list length: ', image_list_length)
            # target_image_list = []
            # for j in range(image_list_length):
            #     target_image_list.append(image_seq_texture[texture_name][frame_idx_texture[texture_name]:frame_idx_texture[texture_name] + 1].repeat(x.shape[0], 1, 1, 1))
            target_image_list_motion = []
            if (idx_vid > target_image_seq.shape[0]):
                idx_vid = 0
            for j in range(idx_vid, idx_vid + image_list_length):
                target_image_list_motion.append(target_image_seq[j:j + 1].repeat(x.shape[0] , 1, 1, 1))
            # import pdb; pdb.set_trace()
            
            video_loss, video_summary = video_motion_loss_fn.forward(
                generated_image_list=generated_image_list_motion, 
                target_image_list=target_image_list_motion,
                return_summary=True
            )
            overflow_loss = (x - x.clamp(-5.0, 5.0)).abs().sum()
            texture_loss = texture_loss_fn(target_images, image_after_nca)[0]
            video_loss = video_motion_loss_fn.motion_weight * video_loss/(len(target_images)*(image_list_length - 1))
            loss = texture_loss + overflow_loss + video_loss
            print(f'epoch: {epoch}, texture_loss: {texture_loss.item():.4f}, overflow: {overflow_loss.item():.4f}, video_loss: {video_loss.item():.4f}')
            print('motion weight: ', video_motion_loss_fn.motion_weight)

            loss_log_dict['overflow'].append(overflow_loss.item())
            loss_log_dict['appearance'].append(texture_loss.item())

            loss_log_dict['video_motion'].append(video_loss.item())  ##

            # if (seed_injection == False):
            #     loss_log_dict['video_motion'].append(min(video_loss.item(), 15.0))
            
            # if (epoch == args.nca_warmup_iter): 
            #     video_motion_loss_fn.set_video_weight( medium_mt=np.median(loss_log_dict['video_motion']))
                
            #     with torch.no_grad():
            #         loss.backward()
            #         opt.step()
            #         opt.zero_grad()
            #     del nca
            #     del loss
            #     del opt

            #     nca = DyNCA(**config['model']['attr']).to(device)
            #     pools = {}
            #     with torch.no_grad():
            #         for idx, (_, texture_name) in enumerate(dataset):
            #             pools[texture_name] = nca.seed(pool_size)
            #             pools[texture_name] = pool_genome(texture_name, pools)

            #     opt = torch.optim.Adam(nca.parameters(), lr=config['training']['lr'])
            #     # lr_sched = torch.optim.lr_scheduler.MultiStepLR(opt,  args.lr_decay_step[0], 0.3)
            #     lr_sched = torch.optim.lr_scheduler.MultiStepLR(opt, **config['training']['scheduler']['attr'])
            #     continue


            opt.zero_grad()
            loss.backward()
            for p in nca.parameters():
                p.grad /= (p.grad.norm() + 1e-8)
            opt.step()
            lr_sched.step()
            
            with torch.no_grad():
                for i, (texture_name, pool_idx) in enumerate(batch_indices):
                    pools[texture_name][pool_idx] = x[i:i+1].detach()


            if (epoch % config['training']['log_interval'] == 0) or (epoch == iterations - 1):
                original_imgs = ((target_images.permute(0, 2, 3, 1).detach().cpu().numpy()/2+0.5) * 255.0).astype(np.uint8)
                for idx, (texture_name, original_img) in enumerate(zip( texture_names, original_imgs)):
                    generated_states = pools[texture_name][:args.pool_size]
                    imgs = nca.to_rgb(generated_states).permute([0, 2, 3, 1]).detach().cpu().numpy()
                    imgs = ((np.clip(imgs, -1, 1)+1)/2.0 * 255.0).astype(np.uint8)
                    imgs = np.hstack([original_img] + [imgs[i] for i in range(args.pool_size)])

                    Image.fromarray(imgs).save(f'{output_dirs[texture_name]}/{texture_name}-video-epoch-{epoch}.png')

                    plot_log_dict = {}
                    plot_log_dict['Overflow Loss'] = (loss_log_dict['overflow'], True, True)
                    plot_log_dict['Texture Loss'] = (loss_log_dict['appearance'], True, True)
                    plot_train_log(plot_log_dict, 2, save_path=f"{config['experiment_path']}/losses.jpg")

                    plot_log_dict = {}
                    plot_log_dict['Motion Texture Loss'] = (loss_log_dict['video_motion'], False, False)
                    plot_train_log(plot_log_dict, 1, save_path=f"{config['experiment_path']}/losses_motion.jpg")

    
        if (epoch % config['training']['log_interval'] == 0) or (epoch == iterations - 1):
            torch.save(nca.state_dict(), os.path.join(config['experiment_path'], f"final_model.pth"))
            nca.eval()
            for idx, (image, texture_name) in enumerate(dataset):
                synthesize_video(nca, texture_name=texture_name, video_length=args.video_length * 20,
                    output_dir=output_dirs[texture_name],
                    target_image_seq_texture=image.unsqueeze(0).to(device),
                    target_image_seq=target_image_seq, video_name='video',
                    nca_step=args.motion_nca_interval // 2,
                    record_loss=True, loss_class=[texture_loss_fn, video_motion_loss_fn],
                    seed_size=(x.shape[2], x.shape[3]), fps=25)
                synthesize_video(nca, texture_name=texture_name, video_length=args.video_length * 20,
                    output_dir=output_dirs[texture_name],
                    target_image_seq_texture=image.unsqueeze(0).to(device),
                    target_image_seq=target_image_seq, video_name='video_large',
                    nca_step=args.motion_nca_interval // 2,
                    record_loss=True, loss_class=[texture_loss_fn, video_motion_loss_fn],
                    seed_size=(x.shape[2]*2, x.shape[3]*2), fps=25)

if __name__ == "__main__":
    args = parser.parse_args()
    with open(args.config, 'r') as stream:
        config = yaml.load(stream, Loader=yaml.FullLoader)

    config['training']['lr'] = 0.001
    config['model']['attr']['c_in'] = args.chn
    config['model']['attr']['fc_dim'] = args.fc_dim
    config['data_dir'] = args.data_dir
    config['training']['batch_size'] = args.data_batch
    config['training']['nca']['pool_size'] = args.pool_size
    config['device'] = 'cuda' if torch.cuda.is_available() else 'cpu'

    # config['model']['attr']['pos_emb'] = args.pos_emb
    config['model']['attr']['perception_scales'] = args.perception_scales
    config['training']['nca']['step_range'] = [80, 144]
    config['model']['motion_video']['motion_weight'] =  args.video_motion_loss_weight

    exp_name = config['experiment_name']
    exp_path = args.save_dir
    config['experiment_path'] = exp_path
    if not os.path.exists(exp_path):
        os.makedirs(exp_path)
    main(config)
