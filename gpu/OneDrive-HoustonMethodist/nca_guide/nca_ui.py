import os
import pygame
import numpy as np
import time
import torch
from PIL import Image
import glob


def load_target_image(path, size=128):
    """Load and preprocess a target image"""
    img = Image.open(path)
    img = img.resize((size, size), Image.Resampling.LANCZOS)
    img_np = np.array(img)
    img_np = img_np / 255.0
    img_np = (img_np - 0.5) / 0.5 # equivalent to transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
    return img_np

def load_image_name(path):
    """Extract image name from path"""
    return os.path.basename(path)

class NCABrush:
    """Brush tool for applying different effects to NCA models"""
    def __init__(self, nca, brush_radius=15):
        self.nca = nca
        self.brush_radius = brush_radius
        
    def apply(self, center_x, center_y):
        """Apply brush at the specified coordinates"""
        center_y, center_x = center_x, center_y

        if hasattr(self.nca, 'x'):
            state_tensor = self.nca.x[0]
        elif hasattr(self.nca, 'state'):
            state_tensor = self.nca.state[0]
        else:
            return

        if self.nca.model_name == "NoiseNCA":
            self._apply_noise_nca_brush(state_tensor, center_x, center_y)
        elif self.nca.model_name in ["VNCA", "MVNCA", "NCA_internal"]:
            self._apply_latent_nca_brush(self.nca.model_name, state_tensor, center_x, center_y)

            
    
    def _apply_noise_nca_brush(self, state_tensor, center_x, center_y):
        """Apply random noise brush for NoiseNCA model"""
        new_state = state_tensor.clone()
        _, height, width = new_state.shape
        
        noise = self.nca.model.seed(1)[0].to(new_state.device)
        radius = self.brush_radius
        min_y = max(0, int(center_y - radius))
        max_y = min(height, int(center_y + radius + 1))
        min_x = max(0, int(center_x - radius))
        max_x = min(width, int(center_x + radius + 1))
        radius_squared = radius * radius
        for y in range(min_y, max_y):
            for x in range(min_x, max_x):
                dist_squared = (y - center_y) ** 2 + (x - center_x) ** 2
                if dist_squared <= radius_squared:
                    new_state[:, y, x] = noise[:, y, x]
        
        self.nca.x[0] = new_state
        self.nca.pool[self.nca.batch_idx[0]] = new_state

    def _apply_latent_nca_brush(self, model_name, state_tensor, center_x, center_y):
        """Apply latent-based brush for VNCA models"""
        # Create a mask for the brush area
        mask = torch.zeros_like(state_tensor[0]).to(state_tensor.device)
        height, width = mask.shape
        radius = self.brush_radius
        radius_squared = radius * radius
        
        min_y = max(0, int(center_y - radius))
        max_y = min(height, int(center_y + radius + 1))
        min_x = max(0, int(center_x - radius))
        max_x = min(width, int(center_x + radius + 1))
        
        for y in range(min_y, max_y):
            for x in range(min_x, max_x):
                dist_squared = (y - center_y) ** 2 + (x - center_x) ** 2
                if dist_squared <= radius_squared:
                    mask[y, x] = 1.0
        
        # Expand mask to all channels
        mask = mask.unsqueeze(0).expand_as(state_tensor)
        
        with torch.no_grad():
            if model_name == "VNCA":
                q_z_given_x = self.nca.model.encode(self.nca.original_x.to(state_tensor.device))
                z_sample = q_z_given_x.rsample((1,))
            elif model_name == "MVNCA":
                temp = self.nca.model.transformer_encoder(self.nca.original_x.to(state_tensor.device))
                q_z_given_x = self.nca.model.encode(temp)
                z_sample = q_z_given_x.rsample((1,))
            elif model_name == "NCA_internal":
                x = self.nca.model.seed(1).to(state_tensor.device)
                texture_name = self.nca.image_path.split('/')[-1].split('-')[0]
                z_sample = self.nca.model.one_genome(texture_name, x)

            img_size = 128
            # z_sample = q_z_given_x.rsample((1,))
            target_state = z_sample.view(1, -1, img_size, img_size)
            rand_y = torch.randint(0, max(1, height - (max_y - min_y)), (1,)).item()
            rand_x = torch.randint(0, max(1, width - (max_x - min_x)), (1,)).item()
            
            new_state = state_tensor.clone()
            for c in range(state_tensor.shape[0]):
                for i, y in enumerate(range(min_y, max_y)):
                    for j, x in enumerate(range(min_x, max_x)):
                        if mask[0, y, x] > 0 and i < (max_y - min_y) and j < (max_x - min_x):
                            try:
                                new_state[c, y, x] = target_state[0, c % target_state.shape[1], 
                                                      min(rand_y + i, img_size-1), 
                                                      min(rand_x + j, img_size-1)]
                            except IndexError:
                                continue
            self.nca.state[0] = new_state
    
    def set_radius(self, radius):
        """Set the brush radius"""
        self.brush_radius = radius


class Slider:
    """Simple slider UI element"""
    def __init__(self, x, y, width, height, min_val, max_val, initial_val, label):
        self.rect = pygame.Rect(x, y, width, height)
        self.min_val = min_val
        self.max_val = max_val
        self.value = initial_val
        self.label = label
        self.active = False

    def handle_event(self, event):
        if event.type == pygame.MOUSEBUTTONDOWN:
            if self.rect.collidepoint(event.pos):
                self.active = True
                self.update_value(event.pos[0])
                return True
        elif event.type == pygame.MOUSEBUTTONUP:
            self.active = False
        elif event.type == pygame.MOUSEMOTION:
            if self.active:
                self.update_value(event.pos[0])
                return True
        return False

    def update_value(self, x_pos):
        rel_x = max(0, min(1, (x_pos - self.rect.x) / self.rect.width))
        self.value = self.min_val + rel_x * (self.max_val - self.min_val)

    def draw(self, surface):
        # Draw slider background
        pygame.draw.rect(surface, (200, 200, 200), self.rect)
        # Draw slider position
        pos_x = self.rect.x + (self.value - self.min_val) / (self.max_val - self.min_val) * self.rect.width
        pos_rect = pygame.Rect(pos_x - 5, self.rect.y, 10, self.rect.height)
        pygame.draw.rect(surface, (100, 100, 100), pos_rect)
        # Draw label and value
        font = pygame.font.SysFont(None, 24)
        label_text = f"{self.label}: {self.value:.4f}" if isinstance(self.value, float) else f"{self.label}: {self.value}"
        label_surface = font.render(label_text, True, (0, 0, 0))
        surface.blit(label_surface, (self.rect.x, self.rect.y - 20))


class NCAUI:
    """UI for visualizing NCA models"""
    def __init__(self, nca_model, test_image_path, width=900, height=680):
        # Initialize pygame
        pygame.init()
        self.screen = pygame.display.set_mode((width, height))
        pygame.display.set_caption(f"NCA Viewer - {nca_model.model_name}")

        # Setup model and images
        self.grid_size = 128
        self.test_image_path = test_image_path
        self.target_image = load_target_image(self.test_image_path)
        self.target_image_name = load_image_name(self.test_image_path)
        self.nca = nca_model

        if self.nca.model_name == "MVNCA":
            self.nca.target_image = self.target_image
            self.nca.reset()
        
        if self.nca.model_name == "NoiseNCA":
            self.nca.target_image = self.target_image
            self.nca.reset()

        if self.nca.model_name == "VNCA":
            self.nca.target_image = self.target_image
            self.nca.reset()

        if self.nca.model_name == "NCA_internal":
            self.nca.target_image = self.target_image
            self.nca.reset()

        # UI setup
        self.width, self.height = width, height
        self.font = pygame.font.SysFont(None, 24)
        self.display_size = 400
        self.display_offset_x = 20
        self.display_offset_y = 20

        # Button setup
        self.button_height = 30
        self.button_width = 120
        self.button_spacing = 10
        self.button_y = self.display_size + 95

        # Create button rectangles 
        self.buttons = {
            "start": pygame.Rect(self.display_offset_x, self.button_y, self.button_width, self.button_height),
            "reset": pygame.Rect(self.display_offset_x + self.button_width + self.button_spacing, self.button_y, self.button_width, self.button_height),
            "load_image": pygame.Rect(self.display_offset_x + (self.button_width + self.button_spacing) * 2, self.button_y, self.button_width, self.button_height),
            "switch_model": pygame.Rect(self.display_offset_x + (self.button_width + self.button_spacing) * 3, self.button_y, self.button_width, self.button_height),
            "brush": pygame.Rect(self.display_offset_x + (self.button_width + self.button_spacing) * 4, self.button_y, self.button_width, self.button_height)
        }

        # Slider positions
        self.speed_slider_y = self.display_size + 160
        self.steps_slider_y = self.display_size + 205
        self.brush_slider_y = self.display_size + 250

        # Initialize UI state
        self.clock = pygame.time.Clock()
        self.fps = 60
        self.running = True
        self.last_update_time = time.time()
        self.update_interval = 0.1
        self.steps_per_update = 1
        self.brush_active = True
        self.brush_radius = 15

        # Initialize sliders
        self.speed_slider = Slider(self.display_offset_x, self.speed_slider_y, self.width - 50, 20, 0.01, 1.0, self.update_interval, "Update Interval (seconds)")
        self.steps_slider = Slider(self.display_offset_x, self.steps_slider_y, self.width - 50, 20, 1, 20, self.steps_per_update, "Steps Per Update")
        self.brush_slider = Slider(self.display_offset_x, self.brush_slider_y, self.width - 50, 20, 1, 30, self.brush_radius, "Brush Radius")

        # Initialize brush
        self.brush = NCABrush(self.nca, self.brush_radius)

    def handle_events(self):
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return False

            # Handle slider events
            if self.speed_slider.handle_event(event) or self.steps_slider.handle_event(event) or self.brush_slider.handle_event(event):
                self.update_interval = self.speed_slider.value
                self.steps_per_update = int(self.steps_slider.value)
                self.brush_radius = int(self.brush_slider.value)
                # Update the brush radius in the NCABrush instance
                self.brush.set_radius(self.brush_radius)
                continue

            # Handle button clicks
            if event.type == pygame.MOUSEBUTTONDOWN:
                # Handle buttons
                if self.buttons["start"].collidepoint(event.pos):
                    self.running = not self.running
                elif self.buttons["reset"].collidepoint(event.pos):
                    self.nca.reset()
                elif self.buttons["load_image"].collidepoint(event.pos):
                    # Use a pygame-based file selection approach instead of tkinter
                    # Get all image files in the data/images directory
                    image_dir = os.path.join(os.getcwd(), "data", "images")
                    image_files = []
                    for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
                        image_files.extend(glob.glob(os.path.join(image_dir, ext)))
                    
                    if image_files:
                        # Simple approach: cycle through available images
                        # Find current image index
                        try:
                            current_idx = image_files.index(self.test_image_path)
                        except ValueError:
                            current_idx = -1
                        
                        # Select next image
                        next_idx = (current_idx + 1) % len(image_files)
                        new_image_path = image_files[next_idx]
                        
                        # Update the image path and reload
                        self.test_image_path = new_image_path
                        self.target_image = load_target_image(self.test_image_path)
                        self.target_image_name = load_image_name(self.test_image_path)
                        
                        # Display a message about the selected image
                        print(f"Loaded image: {self.target_image_name}")
                        
                        # Reset the model with the new image
                        if self.nca.model_name == "MVNCA":
                            self.nca.image_path = new_image_path
                            self.nca.reset()
                            return "reload_image"
                        elif self.nca.model_name == "NoiseNCA":
                            self.nca.target_image = self.target_image
                            self.nca.reset()
                        elif self.nca.model_name == "VNCA":
                            self.nca.image_path = new_image_path
                            self.nca.reset()
                            return "reload_image"
                        elif self.nca.model_name == "NCA_internal":
                            self.nca.image_path = new_image_path
                            self.nca.reset()
                            return "reload_image"
                    else:
                        print("No image files found in data/images directory")
                
                elif self.buttons["switch_model"].collidepoint(event.pos):
                    return "switch_model"
                elif self.buttons["brush"].collidepoint(event.pos):
                    self.brush_active = not self.brush_active
            
                # Apply brush
                if self.brush_active:
                    mouse_x, mouse_y = event.pos
                    if 20 <= mouse_x < 20 + self.display_size and 20 <= mouse_y < 20 + self.display_size:
                        model_x = int((mouse_x - self.display_offset_x) * self.grid_size / self.display_size)
                        model_y = int((mouse_y - self.display_offset_y) * self.grid_size / self.display_size)
                        # self.apply_noise_brush(model_x, model_y)
                        self.brush.apply(model_x, model_y)

            # Handle mouse motion for brush
            if event.type == pygame.MOUSEMOTION and event.buttons[0] and self.brush_active:
                mouse_x, mouse_y = event.pos
                if 20 <= mouse_x < 20 + self.display_size and 20 <= mouse_y < 20 + self.display_size:
                    model_x = int((mouse_x - self.display_offset_x) * self.grid_size / self.display_size)
                    model_y = int((mouse_y - self.display_offset_y) * self.grid_size / self.display_size)
                    # self.apply_noise_brush(model_x, model_y)
                    self.brush.apply(model_x, model_y)

            # Handle keyboard shortcuts
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_SPACE:
                    self.running = not self.running
                elif event.key == pygame.K_r:
                    self.nca.reset()
                elif event.key == pygame.K_b:
                    self.brush_active = not self.brush_active

        return True

    def apply_noise_brush(self, center_x, center_y):
        # Swap x and y coordinates to fix the diagonal issue
        center_y, center_x = center_x, center_y

        # Get the current state
        if hasattr(self.nca, 'x'):
            state_tensor = self.nca.x[0]
        elif hasattr(self.nca, 'state'):
            state_tensor = self.nca.state[0]
        else:
            return

        # Create a new tensor for the state
        new_state = state_tensor.clone()
        _, height, width = new_state.shape

        # Create random noise
        if hasattr(self.nca, 'x'):
            noise = self.nca.model.seed(1)[0].to(new_state.device)
        else:
            noise = torch.rand_like(new_state) * 2.0 - 1.0

        # Apply noise in a circular region
        radius = self.brush_radius
        min_y = max(0, int(center_y - radius))
        max_y = min(height, int(center_y + radius + 1))
        min_x = max(0, int(center_x - radius))
        max_x = min(width, int(center_x + radius + 1))
        radius_squared = radius * radius

        # Apply noise to pixels within the circle
        for y in range(min_y, max_y):
            for x in range(min_x, max_x):
                dist_squared = (y - center_y) ** 2 + (x - center_x) ** 2
                if dist_squared <= radius_squared:
                    new_state[:, y, x] = noise[:, y, x]

        # Update the model state
        if hasattr(self.nca, 'x'):
            self.nca.x[0] = new_state
            self.nca.pool[self.nca.batch_idx[0]] = new_state
        elif hasattr(self.nca, 'state'):
            self.nca.state[0] = new_state


    def update(self):
        current_time = time.time()
        if self.running and (current_time - self.last_update_time) >= self.update_interval:
            self.nca.step(self.steps_per_update)
            self.last_update_time = current_time

    def render(self):
        self.screen.fill((240, 240, 240))

        img = self.nca.get_image()
        pil_img = Image.fromarray(img)
        pil_img = pil_img.resize((self.display_size, self.display_size), Image.Resampling.LANCZOS)
        img = np.array(pil_img)
        surface = pygame.Surface((self.display_size, self.display_size))
        pygame.surfarray.blit_array(surface, img)
        self.screen.blit(surface, (20, 20))

        # Display target image
        if self.target_image is not None:
            # Convert from [-1, 1] to [0, 255] for display
            target_img = (np.clip(self.target_image, -1, 1) * 0.5 + 0.5) * 255.0
            target_img = target_img.astype(np.uint8)
            target_pil = Image.fromarray(target_img)
            target_pil = target_pil.resize((self.display_size, self.display_size), Image.Resampling.LANCZOS)
            target_img = np.array(target_pil)
            target_surface = pygame.Surface((self.display_size, self.display_size))
            pygame.surfarray.blit_array(target_surface, target_img)
            self.screen.blit(target_surface, (self.display_size + 80, 20))

        # Draw status text and info
        status = "Running" if self.running else "Paused"
        self.screen.blit(self.font.render(f"Status: {status} (Space to toggle, R to reset)", True, (0, 0, 0)), (20, self.display_size + 70))
        self.screen.blit(self.font.render(f"Epoch: {self.nca.epoch}", True, (0, 0, 0)), (20, 1))
        self.screen.blit(self.font.render(f"Target Image: {self.target_image_name}", True, (0, 0, 0)), (self.display_size + 80, 1))
        self.screen.blit(self.font.render(f"Model: {self.nca.model_name}", True, (0, 0, 0)), (20, self.display_size + 30))

        # Draw sliders
        self.speed_slider.draw(self.screen)
        self.steps_slider.draw(self.screen)

        # Draw buttons
        button_configs = [
            ("start", (100, 200, 100) if self.running else (200, 100, 100), "Stop" if self.running else "Start"),
            ("reset", (100, 100, 200), "Reset Noise"),
            ("load_image", (200, 100, 200), "Next Image"),
            ("switch_model", (150, 150, 150), "Switch Model"),
            ("brush", (100, 200, 200) if self.brush_active else (150, 150, 150), "Noise Brush")
        ]

        for name, color, text in button_configs:
            pygame.draw.rect(self.screen, color, self.buttons[name])
            button_text = self.font.render(text, True, (255, 255, 255))
            self.screen.blit(button_text, (self.buttons[name].x + 10, self.buttons[name].y + 5))

        # Draw brush slider
        self.brush_slider.draw(self.screen)

        # Draw brush cursor if active
        if self.brush_active:
            mouse_x, mouse_y = pygame.mouse.get_pos()
            if 20 <= mouse_x < 20 + self.display_size and 20 <= mouse_y < 20 + self.display_size:
                pygame.draw.circle(self.screen, (200, 200, 200), (mouse_x, mouse_y), self.brush_radius * self.display_size / self.grid_size, 2)

        # Update the display
        pygame.display.flip()

    def run(self):
        while True:
            result = self.handle_events()
            if result == False:
                break
            elif result == "switch_model":
                return "switch_model"

            self.update()
            self.render()
            self.clock.tick(self.fps)

        pygame.quit()
        return None