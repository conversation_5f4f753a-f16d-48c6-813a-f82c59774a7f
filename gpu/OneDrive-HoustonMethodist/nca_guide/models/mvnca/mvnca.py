"""
VNCA Module
--------------
This module implements the VNCA model.
"""

import os
import torch
import yaml
from torch.distributions import Normal
import numpy as np
from models.mvnca.model import NCA
from models.mvnca.utils.dataset_omi_norm import TextureDataset
from torch.utils.data import Data<PERSON>oader

def mask_img_name(img_token, name_token, mask_prob=0.75):
    img_mask = (torch.rand(img_token.size(1)) > mask_prob).float()
    img_mask = img_mask.unsqueeze(-1).expand_as(img_token)
    img_token = img_token * img_mask
    name_mask = (torch.rand(name_token.size(1)) > mask_prob).float()
    name_mask = name_mask.unsqueeze(-1).expand_as(name_token)
    name_token = name_token * name_mask

    null_token = torch.zeros(img_token.size(0), 1, 768)
    target_tokens = torch.cat([null_token, img_token, name_token, null_token], dim=1)
    return target_tokens

class SingleImageDataset(TextureDataset):
    def __init__(self, image_path, max_size=128):
        super().__init__('data', max_size=max_size)
        # Override image_paths with just our single image
        if os.path.isfile(image_path):
            self.image_paths = [image_path]
        else:
            image_files = [os.path.join(image_path, f) for f in os.listdir(image_path)
                        if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
            if image_files:
                self.image_paths = [image_files[0]]
            else:
                raise ValueError(f"No image files found in {image_path}")
    def __len__(self):
        return 1

class MVNCA(torch.nn.Module):
    def __init__(self, config_path, device=None, image_path=None):
        super().__init__()
        self.model_name = "MVNCA"
        self.state = None
        self.epoch = 0
        self.device = device if device is not None else torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        with open(config_path, 'r') as stream:
            self.config = yaml.load(stream, Loader=yaml.FullLoader)
        self.chn = self.config['model']['attr']['chn']
        self.image_path = image_path

        # Initialize model parameters from config
        self.batch_size = 1
        self.step_range = self.config['training']['nca']['step_range']
        self.inject_seed_step = self.config['training']['nca']['inject_seed_step']
        self.pool_size = self.config['training']['nca']['pool_size']
        self.img_size = self.config['model']['attr']['img_size']
        self.embedding_chn = self.config['model']['attr']['embedding_chn']

        # Initialize model
        self.model = NCA(
            chn=self.config['model']['attr']['chn'],
            fc_dim=self.config['model']['attr']['fc_dim'],
            z_size=self.config['model']['attr']['z_size'],
            min_steps=self.config['model']['attr']['min_steps'],
            max_steps=self.config['model']['attr']['max_steps'],
            update_prob=self.config['model']['attr']['update_prob'],
            img_size=self.config['model']['attr']['img_size'],
            embedding_chn=self.config['model']['attr']['embedding_chn']
        )

        # Use the provided image path or default to a directory
        if image_path is None:
            image_path = 'data'
        
        dataset = SingleImageDataset(image_path, max_size=128)
        dataloader = DataLoader(dataset, batch_size=1, shuffle=False, num_workers=0)
        
        # Get the first (and only) batch
        for batch_idx, (img, img_token, name_token, texture_name) in enumerate(dataloader):
            target_tokens = mask_img_name(img_token, name_token, mask_prob=0.50).to(device)
            self.original_x = target_tokens
            break  # Only process the first batch
        
        self.state = self.reset()


    def reset(self):
        """Reset the model state"""
        dataset = SingleImageDataset(self.image_path, max_size=128)
        dataloader = DataLoader(dataset, batch_size=1, shuffle=False, num_workers=0)
        for batch_idx, (img, img_token, name_token, texture_name) in enumerate(dataloader):
            target_tokens = mask_img_name(img_token, name_token, mask_prob=0.50).to(self.device)
            self.original_x = target_tokens
            break  # Only process the first batch
        with torch.no_grad():
            _, _, self.state = self.model(self.original_x)
            self.epoch = 0
        return self.state

    def step(self, steps=1):
        """Take steps in the model"""
        time_step = self.epoch
        if time_step > 24:
            time_step = 24

        with torch.no_grad():
            for _ in range(steps):
                states = self.model.decode(self.state, time_step)
                self.state = states[-1]
                self.epoch += 1

    def get_image(self):
        """Get the current image as a numpy array"""
        with torch.no_grad():

            rgb_state = self.model.to_rgb(self.state.clone())
            img = rgb_state[0].permute(1, 2, 0).detach().cpu().numpy()
            img = ((np.clip(img, -1, 1)+1)/2.0 * 255.0).astype(np.uint8)

            return img
