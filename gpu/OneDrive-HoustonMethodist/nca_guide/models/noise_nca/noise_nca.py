"""
NoiseNCA Module
--------------
This module implements the NoiseNCA model.
"""

import torch
import os
import yaml
from models.noise_nca.models import NCA
import numpy as np

class NoiseNCA(torch.nn.Module):
    """NoiseNCA model implementation"""
    
    def __init__(self, config_path, device=None):
        """Initialize the NoiseNCA model"""
        super().__init__()
        
        self.device = device if device is not None else torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Load config
        with open(config_path, 'r') as stream:
            self.config = yaml.load(stream, Loader=yaml.FullLoader)
        
        # Initialize model parameters from config
        self.batch_size = self.config['training']['batch_size']  # 4
        self.step_range = self.config['training']['nca']['step_range']  # [32, 128]
        self.inject_seed_step = self.config['training']['nca']['inject_seed_step']  # 8
        self.pool_size = self.config['training']['nca']['pool_size']  # 256
        
        # Initialize model
        self.model = NCA(
            chn=self.config['model']['attr']['chn'],
            fc_dim=self.config['model']['attr']['fc_dim'],
            device=self.device
        )
        
        # Initialize pool and batch
        with torch.no_grad():
            self.pool = self.model.seed(self.pool_size).to(self.device)
            self.batch_idx = torch.randint(0, self.pool_size, (self.batch_size,), device=self.device)
            self.x = self.pool[self.batch_idx]
        
        # Initialize target image
        self.target_image = None
        
        # Initialize epoch counter
        self.epoch = 0
        
        # Model name for UI
        self.model_name = "NoiseNCA"

    def reset(self):
        """Reset the model to a random state"""
        with torch.no_grad():
            self.pool = self.model.seed(self.pool_size).to(self.device)
            self.batch_idx = torch.randint(0, self.pool_size, (self.batch_size,), device=self.device)
            self.x = self.pool[self.batch_idx]
            self.epoch = 0
    
    def step(self, steps=1):
        """Take steps in the model"""
        with torch.no_grad():
            for _ in range(steps):
                self.x = self.model(self.x)
                self.pool[self.batch_idx] = self.x
                self.epoch += 1
    
    def get_image(self):
        """Get the current image as a numpy array"""
        with torch.no_grad():
            rgb = self.model.to_rgb(self.x[:1])
            img = rgb[0].permute(1, 2, 0).detach().cpu().numpy()
            img = (np.clip(img, 0, 1) * 255.0).astype(np.uint8)
            return img
