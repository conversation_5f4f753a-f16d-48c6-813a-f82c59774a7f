import os
import torch

from nca_ui import NCAUI, load_target_image

from models.nca_internal.nca_internal import NCA_internal
from models.noise_nca.noise_nca import NoiseNCA
from models.mvnca.mvnca import MVNCA
from models.vnca.vnca import VNCA




# data_dir = 'data/images_internal/ADI_ADI-TCGA-ESQTRVLE.jpg'
data_dir = 'data/images/ADI-TCGA-ATQCIAPK.jpg'

config_noise_nca='models/noise_nca/Noise-NCA.yml'
noise_nca_weights = 'models/noise_nca/final_model.pth'

config_mvnca='models/mvnca/MVNCA.yml'
mvnca_weights = 'models/mvnca/final_model_epoch150.pth'

config_vnca = 'models/vnca/VNCA.yml'
vnca_weights = 'models/vnca/final_model_epoch20.pth'

config_nca_internal = 'models/nca_internal/Noise-NCA.yml'
nca_internal_weights = 'models/nca_internal/final_model.pth'
# nca_internal_weights = 'models/nca_internal/final_model_ot.pth'
# nca_internal_weights = 'models/nca_internal/final_model_mask.pth'



def main():
    device = torch.device('cpu')

    # Initialize models
    noise_nca = NoiseNCA(config_path=config_noise_nca, device=device)
    state_dict = torch.load(noise_nca_weights, map_location=device)
    noise_nca.model.load_state_dict(state_dict, strict=False)
    # Set initial target image for NoiseNCA
    noise_nca.target_image = load_target_image(data_dir)
    
    mvnca = MVNCA(config_path=config_mvnca, device=device, image_path=data_dir)
    state_dict = torch.load(mvnca_weights, map_location=device)
    mvnca.model.load_state_dict(state_dict, strict=False)

    vnca = VNCA(config_path=config_vnca, device=device, image_path=data_dir)
    state_dict = torch.load(vnca_weights, map_location=device)
    vnca.model.load_state_dict(state_dict, strict=False)

    nca_internal = NCA_internal(config_path=config_nca_internal, device=device, image_path=data_dir)
    state_dict = torch.load(nca_internal_weights, map_location=device)
    nca_internal.model.load_state_dict(state_dict, strict=False)

    print("Models initialized successfully")

    # Start with MVNCA model
    current_model = nca_internal
    current_image_path = data_dir

    # Run the UI with model switching
    while True:
        ui = NCAUI(current_model, current_image_path)
        result = ui.run()
        if result == "switch_model":
            current_image_path = ui.test_image_path
            if current_model == noise_nca:
                mvnca.image_path = current_image_path
                mvnca.reset()
                current_model = mvnca
            elif current_model == mvnca:
                vnca.image_path = current_image_path
                vnca.reset()
                current_model = vnca
            elif current_model == vnca:
                nca_internal.image_path = current_image_path
                nca_internal.reset()
                current_model = nca_internal
            elif current_model == nca_internal:
                noise_nca.image_path = current_image_path
                noise_nca.reset()
                current_model = noise_nca
            print(f"Switched to {current_model.model_name}")

        elif result == "reload_image":
            current_image_path = ui.test_image_path
            print(f"Loading new image: {os.path.basename(current_image_path)}")
            if current_model.model_name == "MVNCA":
                mvnca.image_path = current_image_path
                mvnca.reset()
                current_model = mvnca
                print(f"MVNCA model reinitialized with epoch {current_model.epoch}")
            elif current_model.model_name == "NoiseNCA":
                noise_nca.target_image = load_target_image(current_image_path)
                noise_nca.reset()
                current_model = noise_nca
                print(f"NoiseNCA model reset with epoch {current_model.epoch}")
            elif current_model.model_name == "VNCA":
                vnca.image_path = current_image_path
                vnca.reset()
                current_model = vnca
                print(f"VNCA model reset with epoch {current_model.epoch}")
            elif current_model.model_name == "NCA_internal":
                nca_internal.image_path = current_image_path
                nca_internal.reset()
                current_model = nca_internal
                print(f"NCA_internal model reset with epoch {current_model.epoch}")
        elif result is None:
            break


if __name__ == "__main__":
    main()
