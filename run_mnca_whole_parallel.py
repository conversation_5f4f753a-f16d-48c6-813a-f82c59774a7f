import os
import argparse
import yaml
from tqdm import tqdm
import torch
from src.utils import *
import numpy as np
from PIL import Image
import copy
import json
import scanpy as sc
import random
import warnings
from models_mnca_varker import DyNCA
from collections import defaultdict
from torch.utils.data import Dataset, DataLoader, DistributedSampler
import torchvision.transforms as transforms
import torch.multiprocessing as mp
from src.loss import TextureLoss, GramOmicLoss
from src.dynca_model.vector_field_loss import get_motion_vector_field_by_name
from src.motion_loss import MotionLoss
from src.dataset_spot_select import SpotDataset, ZeroTextEmbeddingDataset, ZeroImgEmbeddingDataset, CompositeDataset
warnings.filterwarnings('ignore', category=UserWarning, module='anndata')

parser = argparse.ArgumentParser()
parser.add_argument('--config', type=str, default='configs/MNCA.yml', help="Path to the config file")
parser.add_argument('--save_dir', type=str, default='results/MNCA/mnca_whole/mnca_triple_fc_96_layer_2', help="Texture images directory")
parser.add_argument('--data_batch', type=int, default=4, help="Number of images per batch")
parser.add_argument('--chn', type=int, default=50, help="Data training channel")
parser.add_argument('--pool_size', type=int, default=4, help="Pool size for each image")
parser.add_argument('--fc_dim', type=int, default=96, help="Latent dim for decoder")
parser.add_argument('--mask_prob', type=float, default=0.3, help="Mask probability")
parser.add_argument('--n_layers', type=int, default=2, help="Number of layers in the update network")
parser.add_argument("--HF_sample_name", type=str, default='HF', help="Name of the HF sample")
parser.add_argument("--HEST_sample_name", type=str, default='HEST', help="Name of the HEST sample")
parser.add_argument("--sample_size", type=int, default=1000, help="Number of samples to use")
parser.add_argument("--embed_dim", type=int, default=20, help="Dimension of the text embedding")
parser.add_argument("--pos_emb", type=str, default='CPE', choices=['None', 'CPE'],help="The positional embedding mode to use. CPE (Cartesian), or None")
parser.add_argument("--perception_scales", type=list, default=[0,1,2,3,4,5], help="Perception scales for the positional embedding")
parser.add_argument('--local_rank', default=0, type=int, help='local device id')

# nohup python run_mnca.py &
# CUDA_VISIBLE_DEVICES=0,1,2 nohup python run_mnca_whole_parallel.py > output.log 2>&1 &
# CUDA_VISIBLE_DEVICES=3 python run_mnca_whole_parallel.py


def main(rank, world_size, args, config):
    # os.environ['MASTER_ADDR'] = 'localhost'
    # os.environ['MASTER_PORT'] = '12355'
    torch.distributed.init_process_group(backend='nccl', init_method='env://', world_size=world_size, rank=rank)
    
    # log
    if rank == 0:
        print(args.__dict__)
        args_log = copy.deepcopy(args.__dict__)
        with open(f'{config["experiment_path"]}/args.txt', 'w') as f:
            json.dump(args_log, f, indent=2)
    
    # loss
    loss_fn = TextureLoss(**config['loss']['attr']).to(device)
    
    # data
    dataset = SpotDataset(args.HF_sample_name, args.HEST_sample_name, args.embed_dim, sample_size=args.sample_size)
    zero_text_dataset = ZeroTextEmbeddingDataset(args.HF_sample_name, args.HEST_sample_name, args.embed_dim, sample_size=args.sample_size)
    zero_img_dataset = ZeroImgEmbeddingDataset(args.HF_sample_name, args.HEST_sample_name, args.embed_dim, sample_size=args.sample_size)
    composite_dataset = CompositeDataset([dataset, zero_text_dataset, zero_img_dataset])
    sampler = DistributedSampler(composite_dataset, num_replicas=world_size, rank=rank)
    dataloader = DataLoader(composite_dataset, batch_size=config['training']['batch_size'], 
                            shuffle=False, sampler=sampler, num_workers=4, drop_last=False)
    
    # model
    nca = DyNCA(**config['model']['attr']).to(device)
    nca = torch.nn.parallel.DistributedDataParallel(nca, device_ids=[rank])
    opt = torch.optim.Adam(nca.parameters(), config['training']['lr'])
    if 'type' not in config['training']['scheduler'] or config['training']['scheduler']['type'] == 'MultiStep':
        lr_sched = torch.optim.lr_scheduler.MultiStepLR(opt, **config['training']['scheduler']['attr'])
    elif config['training']['scheduler']['type'] == 'Cyclic':
        lr_sched = torch.optim.lr_scheduler.CyclicLR(opt, **config['training']['scheduler']['attr'])
    iterations = config['training']['iterations']
    step_range = config['training']['nca']['step_range']
    inject_seed_step = config['training']['nca']['inject_seed_step']
    pool_size = config['training']['nca']['pool_size']
    embed_dim = config['model']['attr']['embed_dim']
    
    pools = {}
    output_dirs = {}
    for idx, (_, img_embedding,  text_embedding, texture_name) in enumerate(composite_dataset):
        pools[texture_name] = nca.seed(pool_size).to(device)
        text_embedding = text_embedding.reshape(1, embed_dim, 1, 1).expand(pool_size, -1, 128, 128).to(device) 
        pools[texture_name][:, -embed_dim:, :, :] = text_embedding
        img_embedding = img_embedding.reshape(1, embed_dim, 1, 1).expand(pool_size, -1, 128, 128).to(device) 
        pools[texture_name][:, -2*embed_dim:-embed_dim:, :, :] = img_embedding
        output_dir = os.path.join(config['experiment_path'],  texture_name)
        os.makedirs(output_dir, exist_ok=True)
        output_dirs[texture_name] = output_dir
    replacer = RandomPatchReplacer(min_percent=0.1, max_percent=args.mask_prob)

    for epoch in range(iterations):
        sampler.set_epoch(epoch)
        for idx, (target_images, img_embeddings, text_embeddings, texture_names) in enumerate(dataloader):
            target_images = target_images.to(device)
            
            batch_x = []
            batch_indices = []
            
            for i, texture_name in enumerate(texture_names):
                pool = pools[texture_name]
                pool_idx = np.random.choice(pool_size, 1, replace=False) 
                x = pool[pool_idx] 
                
                new = nca.seed(1).to(device)
                text_embedding = text_embeddings[i].reshape(1, embed_dim, 1, 1).expand(1, -1, 128, 128).to(device) 
                img_embedding = img_embeddings[i].reshape(1, embed_dim, 1, 1).expand(1, -1, 128, 128).to(device) 
                new[:, -embed_dim:, :, :] = text_embedding
                new[:, -2*embed_dim:-embed_dim:, :, :] = img_embedding
                x = replacer.apply(x, new)
                if epoch % inject_seed_step == 0:
                    x = new
                batch_x.append(x)
                batch_indices.append((texture_name, pool_idx))
            
            x = torch.cat(batch_x, dim=0)
            
            step_n = np.random.randint(step_range[0], step_range[1])
            x, image_after_nca = nca.forward_nsteps(x, step_n, update_rate=0.5)
            
            overflow_loss = (x - x.clamp(-5.0, 5.0)).abs().sum()
            texture_loss = loss_fn(target_images, image_after_nca)[0]
            loss_log_dict['overflow'].append(overflow_loss.item())
            loss_log_dict['appearance'].append(texture_loss.item())
            if rank == 0:
                print(f'epoch: {epoch}, texture_loss: {texture_loss.item():.4f}, overflow: {overflow_loss.item():.4f}')

            opt.zero_grad()
            loss.backward()
            for p in nca.parameters():
                if p.grad is not None:
                    p.grad /= (p.grad.norm() + 1e-8)
            opt.step()
            lr_sched.step()
            
            with torch.no_grad():
                for i, (texture_name, pool_idx) in enumerate(batch_indices):
                    pools[texture_name][pool_idx] = x[i:i+1].detach()

            if (epoch % config['training']['log_interval'] == 0) or (epoch == iterations - 1):
                original_imgs = ((target_images.permute(0, 2, 3, 1).detach().cpu().numpy()/2+0.5) * 255.0).astype(np.uint8)
                for idx, (texture_name, original_img) in enumerate(zip(texture_names, original_imgs)):
                    generated_states = pools[texture_name][:args.pool_size]
                    imgs = nca.to_rgb(generated_states).permute([0, 2, 3, 1]).detach().cpu().numpy()
                    imgs = ((np.clip(imgs, -1, 1)+1)/2.0 * 255.0).astype(np.uint8)
                    imgs = np.hstack([original_img] + [imgs[i] for i in range(args.pool_size)])
                    Image.fromarray(imgs).save(f'{output_dirs[texture_name]}/{texture_name}-epoch-{epoch}.png')
    
        if (epoch % config['training']['log_interval'] == 0) or (epoch == iterations - 1):
            if rank == 0:
                torch.save(nca.state_dict(), os.path.join(config['experiment_path'], f"final_model.pth"))
                vector_field_dir = os.path.join(config['experiment_path'])
                plot_log_dict = {}
                plot_log_dict['Overflow Loss'] = (loss_log_dict['overflow'], True, True)
                plot_log_dict['Texture Loss'] = (loss_log_dict['appearance'], True, True)
                plot_train_log(plot_log_dict, 2, save_path=f"{vector_field_dir}/Base_losses.jpg")

if __name__ == "__main__":
    args = parser.parse_args()
    with open(args.config, 'r') as stream:
        config = yaml.load(stream, Loader=yaml.FullLoader)
    config['training']['lr'] = 0.001
    config['model']['attr']['c_in'] = args.chn
    config['model']['attr']['fc_dim'] = args.fc_dim
    config['model']['attr']['embed_dim'] = args.embed_dim
    config['training']['batch_size'] = args.data_batch
    config['training']['nca']['pool_size'] = args.pool_size
    
    config['device'] = f'cuda:{args.local_rank}' if torch.cuda.is_available() else 'cpu'
    config['loss']['attr']['device'] = config['device']
    config['model']['attr']['device'] = config['device']
    
    config['model']['attr']['perception_scales'] = args.perception_scales
    config['model']['attr']['n_layers'] = args.n_layers
    exp_name = config['experiment_name']
    exp_path = args.save_dir
    config['experiment_path'] = exp_path
    if not os.path.exists(exp_path):
        os.makedirs(exp_path)
    
    world_size = len(os.getenv('CUDA_VISIBLE_DEVICES').split(',')) # torch.cuda.device_count()
    mp.spawn(main, args=(world_size, args, config), nprocs=world_size, join=True)