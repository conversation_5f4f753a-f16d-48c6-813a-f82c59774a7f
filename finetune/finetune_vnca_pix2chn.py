import os
import io
import requests
from matplotlib.colors import rgb2hex
import os
import matplotlib.pyplot as plt
import numpy as np
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import umap
import torch
import yaml
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
from PIL import Image
from matplotlib.patches import Patch
from tqdm import tqdm
import torch.nn as nn

from models_vnca import VNCA
from utils import TextureLoss
# from models_vnca_guide_time import VNCA


device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

def L1_dist(x, y):
    # x : shape [batch, dim]
    # y : shape [num_classes, dim]
    # dist : [batch, num_classes]
    dist = torch.abs(x[:, None, :] - y).mean()    
    return dist

def imread(url, max_size=None, mode=None):
    if url.startswith(('http:', 'https:')):
        # wikimedia requires a user agent
        headers = {
            "User-Agent": "Requests in Colab/0.0 (https://colab.research.google.com/; <EMAIL>) requests/0.0"
        }
        r = requests.get(url, headers=headers)
        f = io.BytesIO(r.content)
    else:
        f = url
    img = Image.open(f)
    if max_size is not None:
        img.thumbnail((max_size, max_size), Image.LANCZOS)  # preserves aspect ratio
    if mode is not None:
        img = img.convert(mode)
    img = np.float32(img) / 255.0
    return img


class TextureDataset(Dataset):
    def __init__(self, data_dir, max_size=128):
        self.image_paths = [os.path.join(data_dir, f) for f in os.listdir(data_dir)
                          if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        self.transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize(max_size),
            transforms.ToTensor(), # C H W, i.e., permute(2, 0, 1)
            transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
        ])

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        img = imread(self.image_paths[idx], max_size=None)  # Resize is handled in transform
        img = self.transform(img)
        random_channels = (torch.rand(9, 128, 128) - 0.5) * 0.25
        img = torch.cat([img, random_channels], dim=0)
        texture_name = os.path.splitext(os.path.basename(self.image_paths[idx]))[0]
        return img, texture_name


class RandomPatchReplacer:
    def __init__(self, min_percent, max_percent):
        self.min_percent = min_percent
        self.max_percent = max_percent
    
    def apply(self, data, mask=None):
        result = data.clone()
        batch_size, channels, height, width = data.shape
        
        for b in range(batch_size):
            # 10% - 30% replace
            h_percent = torch.rand(1).item() * (self.max_percent - self.min_percent) + self.min_percent
            w_percent = torch.rand(1).item() * (self.max_percent - self.min_percent) + self.min_percent
            patch_height = int(height * h_percent)
            patch_width = int(width * w_percent)
            
            # left top point
            start_h = torch.randint(0, height - patch_height + 1, (1,)).item()
            start_w = torch.randint(0, width - patch_width + 1, (1,)).item()
            
            if mask is None:
                patch = (torch.rand(channels, patch_height, patch_width, device=data.device) - 0.5) * 2.0
            else:
                patch = mask[b, :, start_h:start_h+patch_height, start_w:start_w+patch_width]
            
            result[b, :, start_h:start_h+patch_height, start_w:start_w+patch_width] = patch
        
        return result


class PoolManager:
    def __init__(self, model, texture_names, device, pool_size):
        self.model = model
        self.device = device
        self.pool_size = pool_size
        self.texture_names = texture_names

        self.pools_img = {name: None for name in self.texture_names}
        self.pools_mask = {name: None for name in self.texture_names}
        self.pools_img_mask = {name: None for name in self.texture_names}
        self.q_z_given_x_all = {name: None for name in self.texture_names}
        
        self.replacer = RandomPatchReplacer(min_percent=0.1, max_percent=0.3)
    
    def initialize_pools(self, dataloader):
        with torch.no_grad():
            for idx, (targets, texture_names) in enumerate(dataloader):
                target_process = targets.to(self.device)
                
                for i, texture_name in enumerate(texture_names):
                    single_target = target_process[i:i+1]
                    q_z_given_x = self.model.encode(single_target)
                    z_samples = q_z_given_x.rsample((self.pool_size,)).view(self.pool_size, -1, 128, 128)
                    self.pools_img[texture_name] = z_samples
                    self.q_z_given_x_all[texture_name] = q_z_given_x
                    
                    loc = q_z_given_x.loc.view(1, -1, 128, 128)
                    scale = q_z_given_x.scale.view(1, -1, 128, 128)
                    
                    chn_loc = loc.mean(dim=(2, 3))
                    chn_scale = scale.mean(dim=(2, 3))
                    
                    chn_sample = torch.distributions.Normal(chn_loc, chn_scale).sample((self.pool_size, 128, 128)).to(self.device)
                    self.pools_mask[texture_name] = chn_sample.view(self.pool_size, 128, 128, -1).permute(0, 3, 1, 2)
                    
                    self.pools_img_mask[texture_name] = self.replacer.apply(self.pools_img[texture_name], self.pools_mask[texture_name])
         # self.pools_mask.get('TUM-TCGA-AMLIWPII').shape [4, 12, 128, 128]
         # self.pools_img.get('TUM-TCGA-AMLIWPII').shape [4, 12, 128, 128]
         # self.pools_img_mask.get('TUM-TCGA-AMLIWPII').shape [4, 12, 128, 128]
    
    def get_batch_samples(self, texture_names, epoch, inject_seed_step, targets):
        batch_x = []
        batch_indices = []
        for i, texture_name in enumerate(texture_names):
            pool_target = self.pools_img_mask[texture_name]
            pool_idx = np.random.choice(self.pool_size, 1, replace=False)
            x = pool_target[pool_idx]
            
            if epoch % inject_seed_step == 0:
                x = self.replacer.apply(self.pools_img[texture_name], self.pools_mask[texture_name][pool_idx])
                self.pools_img_mask[texture_name][pool_idx] = x
            
            batch_x.append(x)
            batch_indices.append((texture_name, pool_idx))
        
        return batch_x, batch_indices
    
    def update_pools(self, batch_indices, generates):
        for i, (texture_name, pool_idx) in enumerate(batch_indices):
            self.pools_img_mask[texture_name][pool_idx] = generates[i:i+1].detach()
    
    def get_distribution(self, texture_names):
        batch_locs = []
        batch_scales = []
        for texture_name in texture_names:
            loc = self.q_z_given_x_all[texture_name].loc
            scale = self.q_z_given_x_all[texture_name].scale
            batch_locs.append(loc)
            batch_scales.append(scale)
        batch_loc = torch.cat(batch_locs, dim=0)
        batch_scale = torch.cat(batch_scales, dim=0)
        
        return torch.distributions.Normal(batch_loc, batch_scale)
    

if __name__ == "__main__":
    config_path = 'finetune/VNCA.yml'
    with open(config_path, 'r') as stream:
        config = yaml.load(stream, Loader=yaml.FullLoader)

    config['training']['lr'] = 0.001
    config['training']['batch_size'] = 4
    config['model']['attr']['min_steps'] = 32
    config['model']['attr']['max_steps'] = 128
    config['data_dir'] = 'data/images_50_model_nine'
    vnca_weights = 'results/VNCA/vnca_50_nine_finetune/final_model_epoch700.pth'
    config['experiment_path'] = 'results/VNCA/vnca_50_nine_finetune_pix2chn'
    if not os.path.exists(config['experiment_path']):
        os.makedirs(config['experiment_path'])

    device = torch.device(config['device'])
    config['loss']['attr']['device'] = device
    config['model']['attr']['device'] = device
    loss_fn = TextureLoss(**config['loss']['attr']).to(device)

    dataset = TextureDataset(config['data_dir'])
    dataloader = DataLoader(dataset, batch_size=config['training']['batch_size'],
                          shuffle=True, num_workers=4, drop_last=False)

    nca = VNCA(**config['model']['attr']).to(device)
    state_dict = torch.load(vnca_weights, map_location=device)
    nca.load_state_dict(state_dict, strict=False)
   
    for name, param in nca.named_parameters():
        if 'encoder' in name:
            param.requires_grad = False
            print(f"Freezing parameter: {name}")

    opt = torch.optim.Adam(nca.parameters(), config['training']['lr'])
    if 'type' not in config['training']['scheduler'] or config['training']['scheduler']['type'] == 'MultiStep':
        lr_sched = torch.optim.lr_scheduler.MultiStepLR(opt, **config['training']['scheduler']['attr'])
    elif config['training']['scheduler']['type'] == 'Cyclic':
        lr_sched = torch.optim.lr_scheduler.CyclicLR(opt, **config['training']['scheduler']['attr'])

    texture_names = [os.path.splitext(f)[0] for f in os.listdir(config['data_dir'])
                   if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
    
    pool = PoolManager(nca, texture_names, device, pool_size=1)
    pool.initialize_pools(dataloader)

    output_dirs = {}
    for name in texture_names:
        output_dir = os.path.join(config['experiment_path'], name)
        os.makedirs(output_dir, exist_ok=True)
        output_dirs[name] = output_dir

    inject_seed_step = config['training']['nca']['inject_seed_step']
    iterations = config['training']['iterations']


    for epoch in tqdm(range(iterations)):
        for batch_idx, (targets, texture_names) in enumerate(dataloader):
            target_images = targets[..., :3, :, :].to(device)

            batch_x, batch_indices = pool.get_batch_samples(texture_names, epoch, inject_seed_step, targets.to(device))
            target_process = torch.cat(batch_x, dim=0)
            states = nca.decode(target_process)
            generates = states[-1]
            q_z_given_x = pool.get_distribution(texture_names)
            # import pdb; pdb.set_trace()


            # Calculate losses
            overflow_loss = (generates - generates.clamp(-5.0, 5.0)).abs().sum()
            generate_images = nca.to_rgb(generates)
            # import pdb; pdb.set_trace()
            texture_loss, _ = loss_fn(target_images, generate_images)
            # mse_loss = L2_dist(generate_images, target_images)
            mse_loss = L1_dist(generate_images, target_images)
            kl_loss = torch.distributions.kl_divergence(q_z_given_x, nca.p_z).sum(dim=1).mean()
            loss = overflow_loss + texture_loss #+ mse_loss #  + kl_loss

            print('epoch: ', epoch, ' batch: ', batch_idx,
                    'texture_loss: ', texture_loss.item(),
                    'overflow_loss: ', overflow_loss.item(),
                    'kl_loss: ', kl_loss.item(),
                    'mse_loss: ', mse_loss.item())
    
            opt.zero_grad()
            loss.backward()
            for p in nca.parameters():
                if p.grad is not None:  # Check if gradient exists
                    p.grad /= (p.grad.norm() + 1e-8)

            opt.step()
            lr_sched.step()

            pool.update_pools(batch_indices, generates)

            if (epoch % config['training']['log_interval'] == 0) or (epoch == iterations - 1):
                with torch.no_grad():
                    generated_state, generated_states = nca.generate(n_samples=config['training']['batch_size'])
                    # Use the final state for visualization
                    outputs = (nca.to_rgb(generated_state)).permute([0, 2, 3, 1]).detach().cpu().numpy()
                outputs = ((np.clip(outputs, -1, 1)+1)/2.0 * 255.0).astype(np.uint8)

                with torch.no_grad():
                    new, q_z_given_x, states = nca(targets.to(device))
                generates = (nca.to_rgb(new)).permute([0, 2, 3, 1]).detach().cpu().numpy()
                generates = ((np.clip(generates,  -1, 1)+1)/2.0 * 255.0).astype(np.uint8)

                original_imgs = ((targets[..., :3, :, :].permute(0, 2, 3, 1).detach().cpu().numpy()/2+0.5) * 255.0).astype(np.uint8)

                pools_imgs = []
                pool_mask_imgs = []
                pool_masks = []
                for i, texture_name in enumerate(texture_names):
                    pool_img = pool.pools_img[texture_name][0:1]  # 取第一个样本
                    pool_img = pool_img[..., :3, :, :]  # 只取前3个通道
                    pool_img = nca.to_rgb(pool_img).permute(0, 2, 3, 1).detach().cpu().numpy()
                    pool_img = ((np.clip(pool_img, -1, 1)+1)/2.0 * 255.0).astype(np.uint8)[0]  
                    pools_imgs.append(pool_img)

                    pool_img_mask = pool.pools_img_mask[texture_name][0:1]  # 取第一个样本
                    pool_img_mask = pool_img_mask[..., :3, :, :]  # 只取前3个通道
                    pool_img_mask = nca.to_rgb(pool_img_mask).permute(0, 2, 3, 1).detach().cpu().numpy()
                    pool_img_mask = ((np.clip(pool_img_mask, -1, 1)+1)/2.0 * 255.0).astype(np.uint8)[0]  
                    pool_mask_imgs.append(pool_img_mask)

                    pool_mask = pool.pools_mask[texture_name][0:1]  # 取第一个样本
                    pool_mask = nca.to_rgb(pool_mask).permute(0, 2, 3, 1).detach().cpu().numpy()
                    pool_mask = ((np.clip(pool_mask, -1, 1)+1)/2.0 * 255.0).astype(np.uint8)[0]  
                    pool_masks.append(pool_mask)
                
                for idx, (output, original_img, generate_image,pool_mask_img, pool_img, pool_mask, texture_name) in enumerate(zip(outputs, original_imgs, generates, pool_mask_imgs, pools_imgs, pool_masks, texture_names)):
                    imgs = np.hstack([original_img] + [generate_image] + [output]+ [pool_img] + [pool_mask_img] + [pool_mask])
                    Image.fromarray(imgs).save(f'{output_dirs[texture_name]}/{texture_name}-epoch-{epoch}.png')

        if (epoch % config['training']['log_interval'] == 0) or (epoch == iterations - 1):
            torch.save(nca.state_dict(), os.path.join(config['experiment_path'], f"final_model_epoch{epoch}.pth"))


# CUDA_VISIBLE_DEVICES=1 python finetune/finetune_vnca_pix2chn.py

