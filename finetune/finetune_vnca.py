import os
import io
import requests
from matplotlib.colors import rgb2hex
import os
import matplotlib.pyplot as plt
import numpy as np
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import umap
import torch
import yaml
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
from PIL import Image
from matplotlib.patches import Patch
from tqdm import tqdm
import torch.nn as nn

from models_vnca import VNCA
from utils import TextureLoss
# from models_vnca_guide_time import VNCA


device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

def L1_dist(x, y):
    # x : shape [batch, dim]
    # y : shape [num_classes, dim]
    # dist : [batch, num_classes]
    dist = torch.abs(x[:, None, :] - y).mean()    
    return dist

def imread(url, max_size=None, mode=None):
    if url.startswith(('http:', 'https:')):
        # wikimedia requires a user agent
        headers = {
            "User-Agent": "Requests in Colab/0.0 (https://colab.research.google.com/; <EMAIL>) requests/0.0"
        }
        r = requests.get(url, headers=headers)
        f = io.BytesIO(r.content)
    else:
        f = url
    img = Image.open(f)
    if max_size is not None:
        img.thumbnail((max_size, max_size), Image.LANCZOS)  # preserves aspect ratio
    if mode is not None:
        img = img.convert(mode)
    img = np.float32(img) / 255.0
    return img




class TextureDataset(Dataset):
    def __init__(self, data_dir, max_size=128):
        self.image_paths = [os.path.join(data_dir, f) for f in os.listdir(data_dir)
                          if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        self.transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize(max_size),
            transforms.ToTensor(), # C H W, i.e., permute(2, 0, 1)
            transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
        ])

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        img = imread(self.image_paths[idx], max_size=None)  # Resize is handled in transform
        img = self.transform(img)
        random_channels = (torch.rand(9, 128, 128) - 0.5) * 0.25
        img = torch.cat([img, random_channels], dim=0)
        texture_name = os.path.splitext(os.path.basename(self.image_paths[idx]))[0]
        return img, texture_name


if __name__ == "__main__":
    config_path = 'finetune/VNCA.yml'
    with open(config_path, 'r') as stream:
        config = yaml.load(stream, Loader=yaml.FullLoader)

    config['training']['lr'] = 0.001
    config['training']['batch_size'] = 4
    config['model']['attr']['min_steps'] = 32
    config['model']['attr']['max_steps'] = 128
    config['data_dir'] = 'data/images_50_model_nine'
    vnca_weights = 'results/VNCA/vnca_50_nine/final_model_epoch3999.pth'
    config['experiment_path'] = 'results/VNCA/vnca_50_nine_finetune'
    if not os.path.exists(config['experiment_path']):
        os.makedirs(config['experiment_path'])

    device = torch.device(config['device'])
    config['loss']['attr']['device'] = device
    config['model']['attr']['device'] = device
    loss_fn = TextureLoss(**config['loss']['attr']).to(device)

    dataset = TextureDataset(config['data_dir'])
    dataloader = DataLoader(dataset, batch_size=config['training']['batch_size'],
                          shuffle=True, num_workers=4, drop_last=False)

    nca = VNCA(**config['model']['attr']).to(device)
    state_dict = torch.load(vnca_weights, map_location=device)
    nca.load_state_dict(state_dict, strict=False)
   
    for name, param in nca.named_parameters():
        if 'encoder' in name:
            param.requires_grad = False
            print(f"Freezing parameter: {name}")

    opt = torch.optim.Adam(nca.parameters(), config['training']['lr'])
    if 'type' not in config['training']['scheduler'] or config['training']['scheduler']['type'] == 'MultiStep':
        lr_sched = torch.optim.lr_scheduler.MultiStepLR(opt, **config['training']['scheduler']['attr'])
    elif config['training']['scheduler']['type'] == 'Cyclic':
        lr_sched = torch.optim.lr_scheduler.CyclicLR(opt, **config['training']['scheduler']['attr'])

    texture_names = [os.path.splitext(f)[0] for f in os.listdir(config['data_dir'])
                   if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
    output_dirs = {}
    pools = {}
    q_z_given_x_all = {}
    for name in texture_names:
        output_dir = os.path.join(config['experiment_path'], name)
        os.makedirs(output_dir, exist_ok=True)
        output_dirs[name] = output_dir
        pools[name] = None
        q_z_given_x_all[name] = None
    
    inject_seed_step = config['training']['nca']['inject_seed_step']
    iterations = config['training']['iterations']

    pool_size = 4
    for idx, (targets, texture_names) in enumerate(dataloader):
        target_process = targets.to(device)

        for i, texture_name in enumerate(texture_names):
            single_target = target_process[i:i+1]
            q_z_given_x = nca.encode(single_target)
            z_samples = q_z_given_x.rsample((pool_size,)).view(pool_size, -1, 128, 128)
            pools[texture_name] = z_samples
            q_z_given_x_all[texture_name] = q_z_given_x
        # import pdb; pdb.set_trace()
        # pools [batch, pool_size, chn, 128, 128]

    for epoch in tqdm(range(iterations)):
        for batch_idx, (targets, texture_names) in enumerate(dataloader):
            target_images = targets[..., :3, :, :].to(device)

            batch_x = []
            batch_indices = []

            for i, texture_name in enumerate(texture_names):
                pool = pools[texture_name]
                pool_idx = np.random.choice(pool_size, 1, replace=False) 
                x = pool[pool_idx] 
                
                if epoch % inject_seed_step == 0:
                    q_z_given_x = nca.encode(targets[i:i+1].to(device))
                    x[:1] = q_z_given_x.rsample((1,)).view(1, -1, 128, 128)
                
                batch_x.append(x)
                batch_indices.append((texture_name, pool_idx))

            target_process = torch.cat(batch_x, dim=0)
            states = nca.decode(target_process)
            # import pdb; pdb.set_trace()
            batch_locs = []
            batch_scales = []
            for texture_name in texture_names:
                loc = q_z_given_x_all[texture_name].loc
                scale = q_z_given_x_all[texture_name].scale
                batch_locs.append(loc)
                batch_scales.append(scale)
            batch_loc = torch.cat(batch_locs, dim=0)
            batch_scale = torch.cat(batch_scales, dim=0)
            q_z_given_x = torch.distributions.Normal(batch_loc, batch_scale)
            generates = states[-1]

            # Calculate losses
            overflow_loss = (generates - generates.clamp(-5.0, 5.0)).abs().sum()
            generate_images = nca.to_rgb(generates)
            # import pdb; pdb.set_trace()
            texture_loss, _ = loss_fn(target_images, generate_images)
            # mse_loss = L2_dist(generate_images, target_images)
            mse_loss = L1_dist(generate_images, target_images)
            kl_loss = torch.distributions.kl_divergence(q_z_given_x, nca.p_z).sum(dim=1).mean()
            loss = overflow_loss + texture_loss #+ mse_loss #  + kl_loss

            print('epoch: ', epoch, ' batch: ', batch_idx,
                    'texture_loss: ', texture_loss.item(),
                    'overflow_loss: ', overflow_loss.item(),
                    'kl_loss: ', kl_loss.item(),
                    'mse_loss: ', mse_loss.item())
    
            opt.zero_grad()
            loss.backward()
            for p in nca.parameters():
                if p.grad is not None:  # Check if gradient exists
                    p.grad /= (p.grad.norm() + 1e-8)

            opt.step()
            lr_sched.step()

            for i, (texture_name, pool_idx) in enumerate(batch_indices):
                pools[texture_name][pool_idx] = generates[i:i+1].detach()


            if (epoch % config['training']['log_interval'] == 0) or (epoch == iterations - 1):
                with torch.no_grad():
                    generated_state, generated_states = nca.generate(n_samples=config['training']['batch_size'])
                    # Use the final state for visualization
                    outputs = (nca.to_rgb(generated_state)).permute([0, 2, 3, 1]).detach().cpu().numpy()
                outputs = ((np.clip(outputs, -1, 1)+1)/2.0 * 255.0).astype(np.uint8)

                with torch.no_grad():
                    new, q_z_given_x, states = nca(targets.to(device))
                generates = (nca.to_rgb(new)).permute([0, 2, 3, 1]).detach().cpu().numpy()
                generates = ((np.clip(generates,  -1, 1)+1)/2.0 * 255.0).astype(np.uint8)

                original_imgs = ((targets[..., :3, :, :].permute(0, 2, 3, 1).detach().cpu().numpy()/2+0.5) * 255.0).astype(np.uint8)
                for idx, (output, original_img, generate_image, texture_name) in enumerate(zip(outputs, original_imgs, generates, texture_names)):
                    imgs = np.hstack([original_img] + [generate_image] + [output])
                    Image.fromarray(imgs).save(f'{output_dirs[texture_name]}/{texture_name}-epoch-{epoch}.png')

        if (epoch % config['training']['log_interval'] == 0) or (epoch == iterations - 1):
            torch.save(nca.state_dict(), os.path.join(config['experiment_path'], f"final_model_epoch{epoch}.pth"))


# python finetune/finetune_vnca.py

