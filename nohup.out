2025-06-18 13:35:30.015733: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-06-18 13:35:30.035016: E external/local_xla/xla/stream_executor/cuda/cuda_fft.cc:467] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered
WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
E0000 00:00:1750271730.055120  468743 cuda_dnn.cc:8579] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered
E0000 00:00:1750271730.061117  468743 cuda_blas.cc:1407] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered
W0000 00:00:1750271730.078778  468743 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.
W0000 00:00:1750271730.078809  468743 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.
W0000 00:00:1750271730.078812  468743 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.
W0000 00:00:1750271730.078815  468743 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.
2025-06-18 13:35:30.083568: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.
To enable the following instructions: AVX2 AVX512F AVX512_VNNI FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.
{'config': 'configs/MNCA.yml', 'save_dir': 'results/MNCA/mnca_whole/mnca_triple_fc_96_layer_2', 'data_batch': 4, 'chn': 50, 'pool_size': 4, 'fc_dim': 96, 'mask_prob': 0.3, 'n_layers': 2, 'HF_sample_name': 'HF', 'HEST_sample_name': 'HEST', 'sample_size': 1000, 'embed_dim': 20, 'pos_emb': 'CPE', 'perception_scales': [0, 1, 2, 3, 4, 5]}
w1.weight            | Shape: torch.Size([96, 202, 1, 1]) | Params: 19,392
w1.bias              | Shape: torch.Size([96]) | Params: 96
w2.weight            | Shape: torch.Size([50, 96, 1, 1]) | Params: 4,800
w2.bias              | Shape: torch.Size([50]) | Params: 50
update_net.conv_first.weight | Shape: torch.Size([96, 202, 3, 3]) | Params: 174,528
update_net.conv_first.bias | Shape: torch.Size([96]) | Params: 96
update_net.conv_last.weight | Shape: torch.Size([50, 96, 1, 1]) | Params: 4,800
update_net.conv_last.bias | Shape: torch.Size([50]) | Params: 50
channel_attention.1.weight | Shape: torch.Size([50, 200, 1, 1]) | Params: 10,000
channel_attention.1.bias | Shape: torch.Size([50]) | Params: 50
channel_attention.3.weight | Shape: torch.Size([200, 50, 1, 1]) | Params: 10,000
channel_attention.3.bias | Shape: torch.Size([200]) | Params: 200
spatial_attention.0.weight | Shape: torch.Size([200, 400, 7, 7]) | Params: 3,920,000
spatial_attention.0.bias | Shape: torch.Size([200]) | Params: 200
spatial_attention.2.weight | Shape: torch.Size([200, 200, 1, 1]) | Params: 40,000
spatial_attention.2.bias | Shape: torch.Size([200]) | Params: 200
