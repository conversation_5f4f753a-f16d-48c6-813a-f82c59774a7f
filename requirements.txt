anndata==0.10.9
anyio @ file:///croot/anyio_1745334642479/work
argon2-cffi @ file:///opt/conda/conda-bld/argon2-cffi_1645000214183/work
argon2-cffi-bindings @ file:///croot/argon2-cffi-bindings_1736182440035/work
array_api_compat==1.11.2
asttokens==3.0.0
async-lru @ file:///croot/async-lru_1699554519285/work
attrs @ file:///croot/attrs_1734533101012/work
babel @ file:///croot/babel_1737454360933/work
backcall @ file:///home/<USER>/src/ci/backcall_1611930011877/work
beautifulsoup4 @ file:///croot/beautifulsoup4-split_1718029820055/work
bleach @ file:///croot/bleach_1732290411627/work
Brotli @ file:///croot/brotli-split_1736182456865/work
certifi @ file:///croot/certifi_1745939216646/work/certifi
cffi @ file:///croot/cffi_1736182485317/work
charset-normalizer @ file:///croot/charset-normalizer_1721748349566/work
comm @ file:///croot/comm_1709322850197/work
contourpy==1.3.0
cycler==0.12.1
debugpy @ file:///croot/debugpy_1736267418885/work
decorator==4.4.2
defusedxml @ file:///tmp/build/80754af9/defusedxml_1615228127516/work
exceptiongroup @ file:///croot/exceptiongroup_1706031385326/work
executing==2.2.0
fastjsonschema @ file:///croot/python-fastjsonschema_1731939362158/work
filelock==3.18.0
fonttools==4.58.0
fsspec==2025.3.2
ftfy==6.3.1
future==1.0.0
gdown==5.2.0
get-annotations==0.1.2
h11 @ file:///croot/h11_1706652277403/work
h5py==3.13.0
httpcore @ file:///croot/httpcore_1706728464539/work
httpx @ file:///croot/httpx_1746747840559/work
huggingface-hub==0.31.2
idna==3.10
imageio==2.37.0
imageio-ffmpeg==0.6.0
importlib_metadata @ file:///croot/importlib_metadata-suite_1732633488278/work
importlib_resources==6.5.2
ipykernel @ file:///croot/ipykernel_1737660677549/work
ipython==8.18.1
ipywidgets==8.1.6
jedi==0.19.2
Jinja2 @ file:///croot/jinja2_1741710844255/work
joblib==1.5.0
json5 @ file:///croot/json5_1730786798687/work
jsonschema @ file:///croot/jsonschema_1728486696720/work
jsonschema-specifications @ file:///croot/jsonschema-specifications_1699032386549/work
jupyter-events @ file:///croot/jupyter_events_1741184577592/work
jupyter-lsp @ file:///croot/jupyter-lsp-meta_1745827015155/work
jupyter_client @ file:///croot/jupyter_client_1737570961872/work
jupyter_core @ file:///croot/jupyter_core_1718818295206/work
jupyter_server @ file:///croot/jupyter_server_1741200034823/work
jupyter_server_terminals @ file:///croot/jupyter_server_terminals_1744706698694/work
jupyterlab @ file:///croot/jupyterlab_1737555423487/work
jupyterlab_pygments @ file:///croot/jupyterlab_pygments_1741124142640/work
jupyterlab_server @ file:///croot/jupyterlab_server_1725865349919/work
jupyterlab_widgets==3.0.15
kiwisolver==1.4.7
legacy-api-wrap==1.4.1
llvmlite==0.43.0
# Editable install with no version control (loki==0.0.1)
-e /condo/wanglab/tmhqxl20/NCA-FM/src/Loki-main/src
MarkupSafe @ file:///croot/markupsafe_1738584038848/work
matplotlib==3.9.2
matplotlib-inline==0.1.7
mistune @ file:///croot/mistune_1741124011532/work
moviepy==1.0.3
mpmath==1.3.0
natsort==8.4.0
nbclient @ file:///croot/nbclient_1741123995822/work
nbconvert @ file:///croot/nbconvert-meta_1741184653315/work
nbformat @ file:///croot/nbformat_1728049424075/work
nest-asyncio @ file:///croot/nest-asyncio_1708532673751/work
networkx==3.2.1
notebook_shim @ file:///croot/notebook-shim_1741707758683/work
numba==0.60.0
numpy==1.25.0
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==*********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==**********
nvidia-cusparse-cu12==**********
nvidia-nccl-cu12==2.20.5
nvidia-nvjitlink-cu12==12.9.41
nvidia-nvtx-cu12==12.1.105
open_clip_torch==2.26.1
opencv-python==*********
openslide-python==1.3.1
overrides @ file:///croot/overrides_1699371140756/work
packaging @ file:///croot/packaging_1734472117206/work
pandas==2.2.3
pandocfilters @ file:///opt/conda/conda-bld/pandocfilters_1643405455980/work
parso==0.8.4
patsy==1.0.1
pexpect==4.9.0
pickleshare @ file:///tmp/build/80754af9/pickleshare_1606932040724/work
pillow==10.4.0
platformdirs @ file:///croot/platformdirs_1744273042065/work
proglog==0.1.12
progressbar==2.5
prometheus_client @ file:///croot/prometheus_client_1744271615306/work
prompt_toolkit==3.0.51
psutil @ file:///croot/psutil_1736367091698/work
ptyprocess==0.7.0
pure_eval==0.2.3
pycparser @ file:///tmp/build/80754af9/pycparser_1636541352034/work
pycpd==2.0.0
Pygments @ file:///croot/pygments_1744664109463/work
pynndescent==0.5.13
pyparsing==3.2.3
PySocks @ file:///tmp/build/80754af9/pysocks_1605305812635/work
python-dateutil @ file:///croot/python-dateutil_1716495738603/work
python-json-logger @ file:///croot/python-json-logger_1734370021104/work
pytz==2025.2
PyYAML @ file:///croot/pyyaml_1728657952215/work
pyzmq @ file:///croot/pyzmq_1734687138743/work
referencing @ file:///croot/referencing_1699012038513/work
regex==2024.11.6
requests @ file:///croot/requests_1730999120400/work
rfc3339-validator @ file:///croot/rfc3339-validator_1683077044675/work
rfc3986-validator @ file:///croot/rfc3986-validator_1683058983515/work
rpds-py @ file:///croot/rpds-py_1736541261634/work
safetensors==0.5.3
scanpy==1.10.3
scikit-learn==1.6.1
scipy==1.13.1
seaborn==0.13.2
Send2Trash @ file:///croot/send2trash_1736540790175/work
session_info==1.0.1
six @ file:///croot/six_1744271502820/work
sniffio @ file:///croot/sniffio_1705431295498/work
soupsieve @ file:///croot/soupsieve_1696347547217/work
stack-data==0.6.3
statsmodels==0.14.4
stdlib-list==0.11.1
sympy==1.14.0
tangram-sc==1.0.4
terminado @ file:///croot/terminado_1671751832461/work
threadpoolctl==3.6.0
timm==1.0.15
tinycss2 @ file:///croot/tinycss2_1738337643607/work
tomli @ file:///opt/conda/conda-bld/tomli_1657175507142/work
torch==2.3.1
torchsummary==1.5.1
torchvision==0.18.1
tornado @ file:///croot/tornado_1733960490606/work
tqdm==4.66.5
traitlets==5.14.3
triton==2.3.1
typing_extensions @ file:///croot/typing_extensions_1734714854207/work
tzdata==2025.2
umap-learn==0.5.7
urllib3 @ file:///croot/urllib3_1737133630106/work
wcwidth==0.2.13
webencodings==0.5.1
websocket-client @ file:///croot/websocket-client_1715878298792/work
widgetsnbextension==4.0.14
zipp @ file:///croot/zipp_1732630741423/work
