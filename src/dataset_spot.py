
import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
import os
import scanpy as sc
from tqdm import tqdm
from matplotlib.colors import rgb2hex
import random
from scipy import stats
from scipy.stats import ranksums
import anndata
import warnings
import torch
import json
import numpy as np
import torch.nn.functional as F 
import torch.nn as nn

import loki.utils
import loki.preprocess
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
from PIL import Image
from sklearn.decomposition import PCA
import joblib  

# device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
device = 'cpu'


class SpotDataset(Dataset):
    def __init__(self, HF_sample_name, HEST_sample_name, embed_dim, sample_size=None, max_size=128, random_seed=42):
        random.seed(random_seed)
        np.random.seed(random_seed)
        torch.manual_seed(random_seed)
        # house_keeping_genes = pd.read_csv('data/housekeeping_genes.csv', index_col = 0)
        # model, preprocess, tokenizer = loki.utils.load_model('data/checkpoint.pt', 'cpu')

        if HF_sample_name != 'None':
            HF_ad = sc.read_h5ad(os.path.join('data/HF/processed_anndata', HF_sample_name+'_filtered.h5ad'))
            # top_k_genes_str = loki.preprocess.generate_gene_df(HF_ad, house_keeping_genes)
            # HF_text_embeddings = np.array(encode_text_df(model, tokenizer, top_k_genes_str, 'label').cpu()) 
            self.hf_size = len(HF_ad.obs.index)

        if HEST_sample_name != 'None':
            self.HEST_sample_name = HEST_sample_name
        #     HEST_ad = sc.read_h5ad(os.path.join('data/HEST/processed_anndata', HEST_sample_name+'_filtered.h5ad'))
        #     top_k_genes_str = loki.preprocess.generate_gene_df(HEST_ad, house_keeping_genes)
        #     HEST_text_embeddings = np.array(encode_text_df(model, tokenizer, top_k_genes_str, 'label').cpu()) ##### run for embedding n*768
            # self.hest_size = len(HEST_ad.obs.index)

        # if HF_sample_name != 'None' and HEST_sample_name != 'None':
        #     self.ad = sc.concat([HF_ad, HEST_ad], join='outer')
        #     self.text_embeddings = np.concatenate([HF_text_embeddings, HEST_text_embeddings], axis=0)
        # elif HF_sample_name != 'None':
        #     self.ad = HF_ad
        #     self.text_embeddings = HF_text_embeddings
        # elif HEST_sample_name != 'None':
        #     self.ad = HEST_ad
        #     self.text_embeddings = HEST_text_embeddings
        # else:
        #     raise ValueError('No sample name provided')

        # if sample_size is not None:
        #     total_size = len(self.index) #ad.obs.
        #     if sample_size > total_size:
        #         raise ValueError(f"Sample size {sample_size} is larger than the total size {total_size}")
        #     self.selected_indices = np.random.choice(total_size, size=sample_size, replace=False)
        # else:
        #     self.selected_indices = np.arange(len(self.index)) #ad.obs.

        self.transform = transforms.Compose([
            transforms.Resize(max_size),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
        ])

        # pca = PCA(n_components=embed_dim)
        # print(self.text_embeddings[self.selected_indices].shape)
        # self.text_embeddings = pca.fit_transform(self.text_embeddings)
        # np.save('data/text_embeddings_pca.npy', self.text_embeddings)
        # np.save('data/obs_index_pca.npy', self.ad.obs.index)
        # np.save('data/pca_selected_indices.npy', self.selected_indices)

        self.index = np.load('data/obs_index_pca.npy', allow_pickle=True) 
        self.text_embeddings = np.load('data/text_embeddings_pca.npy', allow_pickle=True)
        self.selected_indices = np.load('data/pca_selected_indices.npy', allow_pickle=True)
        
    def __len__(self):
        return len(self.selected_indices)
    
    def __getitem__(self, idx):
        real_idx = self.selected_indices[idx]
        if self.hf_size is not None and real_idx < self.hf_size:
            img_path = os.path.join('data/HF/patch', self.index[real_idx] + '_fullres.png')
            # img_path = os.path.join('data/HF/patch', self.ad.obs.index[real_idx] + '_fullres.png')
            texture_name = self.index[real_idx]
        else:
            img_path = os.path.join('data/HEST/spot_patch', self.HEST_sample_name + '_' + self.index[real_idx] + '_fullres.png')
            texture_name = self.HEST_sample_name + '_' + self.index[real_idx]
            # img_path = os.path.join('data/HEST/spot_patch', self.HEST_sample_name + '_' + self.ad.obs.index[real_idx] + '_fullres.png')
            # texture_name = self.HEST_sample_name + '_' + self.ad.obs.index[real_idx]
        img = Image.open(img_path).convert('RGB')
        img = self.transform(img)
        # text_embedding = self.pca.transform(self.text_embeddings[real_idx].reshape(1, -1)).flatten() 
        text_embedding = torch.from_numpy(self.text_embeddings[real_idx]).float()

        return img, text_embedding, texture_name



def encode_text(model, tokenizer, text):
    """
    Encodes text into a normalized feature embedding using a specified model and tokenizer.

    :param model: A model object that provides an `encode_text` method.
    :type model: torch.nn.Module
    :param tokenizer: A tokenizer function that converts the input text into a format suitable for `model.encode_text`.
                      Typically returns token IDs, attention masks, etc. as a torch.Tensor or similar structure.
    :type tokenizer: callable
    :param text: The input text (string or list of strings) to be encoded.
    :type text: str or list[str]
    :return: A PyTorch tensor of shape (batch_size, embedding_dim) containing the L2-normalized text embeddings.
    :rtype: torch.Tensor
    """

    # Convert text to the appropriate tokenized representation
    text_input = tokenizer(text).to(device)

    # Run the model in no-grad mode (not tracking gradients, saving memory and compute)
    with torch.no_grad():
        text_features = model.encode_text(text_input)

    # Normalize embeddings to unit length
    text_embeddings = F.normalize(text_features, p=2, dim=-1)

    return text_embeddings

def encode_text_df(model, tokenizer, df, col_name):
    """
    Encodes text from a specified column in a pandas DataFrame using the given model and tokenizer,
    returning a PyTorch tensor of normalized text embeddings of shape (number_of_rows, embedding_dim).

    :param model: A model object that provides an `encode_text` method.
    :param tokenizer: A tokenizer function that converts input text for the model.
    :param df: A pandas DataFrame containing the text.
    :param col_name: The name of the column in `df` that holds the text.
    :return: A PyTorch tensor of L2-normalized text embeddings, with shape [N, embedding_dim].
    """
    # Create a list of embeddings by encoding each text entry
    embeddings = [
        encode_text(model, tokenizer, text).squeeze()  # Removes all dimensions of size 1
        for text in df[col_name]
    ]
    
    # Stack the embeddings into a single tensor of shape [N, embedding_dim]
    text_embeddings = torch.stack(embeddings, dim=0)
    
    # L2-normalize each embedding vector along the embedding dimension
    text_embeddings = F.normalize(text_embeddings, p=2, dim=-1)
    
    return text_embeddings