
import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
import os
import scanpy as sc
from tqdm import tqdm
from matplotlib.colors import rgb2hex
import random
from scipy import stats
from scipy.stats import ranksums
import anndata
import warnings
import torch
import json
import numpy as np
import torch.nn.functional as F 
import torch.nn as nn

import loki.utils
import loki.preprocess
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
from PIL import Image
from sklearn.decomposition import PCA
import joblib  

# device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
device = 'cpu'
# CUDA_VISIBLE_DEVICES=3 python src/dataset_spot_select.py

class SpotDataset(Dataset):
    def __init__(self, save_dir, HF_sample_name, HEST_sample_name, embed_dim, sample_size=None, max_size=128, random_seed=42):
        random.seed(random_seed)
        np.random.seed(random_seed)
        torch.manual_seed(random_seed)
        house_keeping_genes = pd.read_csv('data/housekeeping_genes.csv', index_col = 0)
        self.model, self.preprocess, tokenizer = loki.utils.load_model('data/checkpoint.pt', 'cpu')

        if HF_sample_name != 'None':
            HF_ad = sc.read_h5ad("data/HE&HF/anndata/HF_filtered.h5ad")
            # HF_ad = HF_ad[~HF_ad.obs['img_path'].isna()]
            self.hf_size = len(HF_ad.obs.index)

        if HEST_sample_name != 'None':
            HEST_ad = sc.read_h5ad("data/HE&HF/anndata/HEST_filtered.h5ad")
            # HEST_ad = HEST_ad[~HEST_ad.obs['img_path'].isna()]
            self.hest_size = len(HEST_ad.obs.index)

        if HF_sample_name != 'None' and HEST_sample_name != 'None':
            self.ad = sc.concat([HF_ad, HEST_ad], join='outer')
        elif HF_sample_name != 'None':
            self.ad = HF_ad
        elif HEST_sample_name != 'None':
            self.ad = HEST_ad
        else:
            raise ValueError('No sample name provided')
        
        top_k_genes_str = loki.preprocess.generate_gene_df(self.ad, house_keeping_genes)
        self.text_embeddings = np.array(encode_text_df(self.model, tokenizer, top_k_genes_str, 'label').cpu())
        self.img_embeddings = encode_images(self.model, self.preprocess, self.ad.obs['img_path'])

        # import pdb; pdb.set_trace()

        # if sample_size is not None:
        #     total_size = self.ad_size #ad.obs.
        #     if sample_size > total_size:
        #         raise ValueError(f"Sample size {sample_size} is larger than the total size {total_size}")
        #     self.selected_indices = np.random.choice(total_size, size=sample_size, replace=False)
        # else:
        #     self.selected_indices = np.arange(self.ad_size) #ad.obs.

        self.transform = transforms.Compose([
            transforms.Resize(max_size),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
        ])

        pca_text = PCA(n_components=embed_dim)
        self.text_embeddings = pca_text.fit_transform(self.text_embeddings)
        os.makedirs(save_dir+'/pca', exist_ok=True)
        np.save(save_dir+'/pca/pca_text_embeddings.npy', self.text_embeddings)
        pca_img = PCA(n_components=embed_dim)
        self.img_embeddings = pca_img.fit_transform(self.img_embeddings)
        np.save(save_dir+'/pca/pca_img_embeddings.npy', self.img_embeddings)

        np.save(save_dir+'/pca/pca_text_components.npy', pca_text.components_)
        np.save(save_dir+'/pca/pca_text_mean.npy', pca_text.mean_)
        np.save(save_dir+'/pca/pca_img_components.npy', pca_img.components_)
        np.save(save_dir+'/pca/pca_img_mean.npy', pca_img.mean_)

        np.save(save_dir+'/pca/obs_index.npy', self.ad.obs['img_path'])
        
    def __len__(self):
        return len(self.ad.obs.index)
    
    def __getitem__(self, idx):
        img_path = os.path.join('data/HE&HF/patch', str(self.ad.obs['img_path'].iloc[idx]) )
        img = Image.open(img_path).convert('RGB')
        img = self.transform(img)
        
        img_embedding_pca = torch.from_numpy(self.img_embeddings[idx]).float()
        text_embedding_pca = torch.from_numpy(self.text_embeddings[idx]).float()
        texture_name = self.ad.obs['img_path'].iloc[idx].split(".")[0]

        return img, img_embedding_pca, text_embedding_pca, texture_name


class ZeroTextEmbeddingDataset(SpotDataset):
    def __getitem__(self, idx):
        img, img_embedding_pca, text_embedding_pca, texture_name = super().__getitem__(idx)
        text_embedding_pca = torch.zeros_like(text_embedding_pca)
        texture_name = f"{texture_name}_text_zero"
        return img, img_embedding_pca, text_embedding_pca, texture_name

class ZeroImgEmbeddingDataset(SpotDataset):
    def __getitem__(self, idx):
        img, img_embedding_pca, text_embedding_pca, texture_name = super().__getitem__(idx)
        img_embedding_pca = torch.zeros_like(img_embedding_pca)
        texture_name = f"{texture_name}_img_zero"
        return img, img_embedding_pca, text_embedding_pca, texture_name

class CompositeDataset(Dataset):
    def __init__(self, datasets):
        self.datasets = datasets
        self.lengths = [len(ds) for ds in datasets]
        self.cumulative_lengths = np.cumsum(self.lengths)
    
    def __len__(self):
        return sum(self.lengths)
    
    def __getitem__(self, idx):
        dataset_idx = np.searchsorted(self.cumulative_lengths, idx, side='right')
        sub_idx = idx - (self.cumulative_lengths[dataset_idx - 1] if dataset_idx > 0 else 0)
        return self.datasets[dataset_idx][sub_idx]


def encode_images(model, preprocess, image_paths):
    """
    Batch–encode a list of image file paths into L2‑normalized embeddings.
    Returns a tensor of shape (N, D).
    """
    # Load & preprocess all images
    imgs = [preprocess(Image.open(os.path.join('data/HE&HF/patch', p))) for p in image_paths]
    batch = torch.stack(imgs, dim=0)         # (N, C, H, W)
    
    with torch.no_grad():
        feats = model.encode_image(batch)                 # (N, D)
    return F.normalize(feats, p=2, dim=-1)                # (N, D)

def encode_one_image(model,preprocess,image_path):
    """
    Batch–encode a list of image file paths into L2‑normalized embeddings.
    Returns a tensor of shape (N, D).
    """
    # Load & preprocess all images
    imgs = preprocess(Image.open(image_path)) 
    if isinstance(imgs, torch.Tensor):
        imgs = [imgs]  # Convert single tensor to list
    batch = torch.stack(imgs, dim=0).to(device)
    # batch = imgs.unsqueeze(0)        # (N, C, H, W)
    
    with torch.no_grad():
        feats = model.encode_image(batch)                 # (N, D)
    return F.normalize(feats, p=2, dim=-1)                # (N, D)

def encode_text(model, tokenizer, text):
    """
    Encodes text into a normalized feature embedding using a specified model and tokenizer.

    :param model: A model object that provides an `encode_text` method.
    :type model: torch.nn.Module
    :param tokenizer: A tokenizer function that converts the input text into a format suitable for `model.encode_text`.
                      Typically returns token IDs, attention masks, etc. as a torch.Tensor or similar structure.
    :type tokenizer: callable
    :param text: The input text (string or list of strings) to be encoded.
    :type text: str or list[str]
    :return: A PyTorch tensor of shape (batch_size, embedding_dim) containing the L2-normalized text embeddings.
    :rtype: torch.Tensor
    """

    # Convert text to the appropriate tokenized representation
    text_input = tokenizer(text).to(device)

    # Run the model in no-grad mode (not tracking gradients, saving memory and compute)
    with torch.no_grad():
        text_features = model.encode_text(text_input)

    # Normalize embeddings to unit length
    text_embeddings = F.normalize(text_features, p=2, dim=-1)

    return text_embeddings

def encode_text_df(model, tokenizer, df, col_name):
    """
    Encodes text from a specified column in a pandas DataFrame using the given model and tokenizer,
    returning a PyTorch tensor of normalized text embeddings of shape (number_of_rows, embedding_dim).

    :param model: A model object that provides an `encode_text` method.
    :param tokenizer: A tokenizer function that converts input text for the model.
    :param df: A pandas DataFrame containing the text.
    :param col_name: The name of the column in `df` that holds the text.
    :return: A PyTorch tensor of L2-normalized text embeddings, with shape [N, embedding_dim].
    """
    # Create a list of embeddings by encoding each text entry
    embeddings = [
        encode_text(model, tokenizer, text).squeeze()  # Removes all dimensions of size 1
        for text in df[col_name]
    ]
    
    # Stack the embeddings into a single tensor of shape [N, embedding_dim]
    text_embeddings = torch.stack(embeddings, dim=0)
    
    # L2-normalize each embedding vector along the embedding dimension
    text_embeddings = F.normalize(text_embeddings, p=2, dim=-1)
    
    return text_embeddings




if __name__ == '__main__':
    dataset = SpotDataset('HF', 'None', 20, sample_size=None)
    dataloader = DataLoader(dataset, batch_size=4, shuffle=True, num_workers=4, drop_last=False)

    for img, img_embedding, text_embedding, texture_name in dataset:
        print(img.shape, img_embedding.shape, text_embedding.shape, texture_name)
        print(img_embedding)
        print(text_embedding)

        # import pdb; pdb.set_trace()



# CUDA_VISIBLE_DEVICES=3 python src/dataset_spot_select.py