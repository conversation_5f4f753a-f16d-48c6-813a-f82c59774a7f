import os
import torch
import numpy as np
import torchvision.transforms.functional as TF
import torch.nn.functional as F
from src.dynca_model.misc.flow_viz import flow_to_image, plot_vec_field
from src.dynca_model.vector_field_loss import get_motion_vector_field_by_name
from src.dynca_model.misc.display_utils import save_train_image
from torchvision import transforms
from PIL import Image, ImageSequence
import cv2
import random
import copy

from src.dynca_model.MSOEmultiscale import MSOEmultiscale

class MotionLoss:
    def __init__(self,
                 motion_model,
                 motion_vector_field_name,
                 motion_img_size=(128, 128),
                 motion_direction_weight=10.0,
                 motion_strength_weight=15.0,
                 motion_weight=4.0,
                 nca_base_num_steps=24.0,
                 device='cuda'):
        # self.motion_model = motion_model
        model = MSOEmultiscale()
        states_dict = torch.load(f'src/dynca_model/{motion_model}_model.pth')
        model.load_state_dict(states_dict)
        self.motion_img_size = motion_img_size
        self.motion_direction_weight = motion_direction_weight
        self.motion_strength_weight = motion_strength_weight
        self.motion_weight = motion_weight
        self.nca_base_num_steps = nca_base_num_steps
        self.device = device

        self.target_motion_vec = get_motion_vector_field_by_name(
            motion_vector_field_name,
            img_size=motion_img_size
        ).to(device)

        self.generated_video_flow = None
        self.generated_flow_vector_field = None
        self.target_flow_vector_field = None

    def get_opticflow(self, image1, image2, nca_num_steps=1, return_summary=False):
        """
            flow: 光流
            flow_img: 光流可视化图像
            flow_vector_field: 光流向量场可视化
        """
        image1_size = image1.shape[2]
        image2_size = image2.shape[2]
        if image1_size != self.motion_img_size[0]:
            image1 = TF.resize(image1, self.motion_img_size)
        if image2_size != self.motion_img_size[0]:
            image2 = TF.resize(image2, self.motion_img_size)

        # MSOEnet接受灰度图像 [0,1]
        x1 = (image1 + 1.0) / 2.0
        x2 = (image2 + 1.0) / 2.0
        x1 = TF.rgb_to_grayscale(x1)
        x2 = TF.rgb_to_grayscale(x2)
        image_cat = torch.stack([x1, x2], dim=-1)
        flow, _ = self.motion_model(image_cat, return_features=True)

        if return_summary:
            flow_img = flow_to_image(flow[0].permute(1, 2, 0).detach().cpu().numpy()).transpose(2, 0, 1)
            # 对光流进行缩放，参考utils/loss/vector_field_loss.py中的实现
            rescaled_flow = flow * self.nca_base_num_steps / nca_num_steps
            mean_rescaled_flow = torch.mean(rescaled_flow, dim=0)
            flow_vector_field = plot_vec_field(mean_rescaled_flow.detach().cpu().numpy(), name='Generated')
        else:
            flow_img = None
            flow_vector_field = None

        return flow, flow_img, flow_vector_field

    def get_cosine_dist(self, optic_flow, target_motion_vec):
        cos_sim = torch.nn.CosineSimilarity(dim=1)
        direction_cos_sim = cos_sim(optic_flow, target_motion_vec)
        direction_loss = 1.0 - torch.mean(direction_cos_sim)
        return direction_loss

    def get_motion_strength_loss(self, optic_flow, target_motion_vec, nca_num_steps=1):
        cos_sim = torch.nn.CosineSimilarity(dim=1)
        # 对光流进行缩放，参考utils/loss/vector_field_loss.py中的实现
        motion_strength = torch.norm(optic_flow, dim=1) * self.nca_base_num_steps / nca_num_steps
        target_strength = torch.norm(target_motion_vec, dim=1)
        motion_strength_loss = torch.abs(motion_strength - target_strength)

        direction_cos_sim = cos_sim(optic_flow, target_motion_vec)
        cos_loss = 1.0 - torch.mean(direction_cos_sim, dim=[1, 2], keepdim=True)

        alpha = (1.0 - torch.clip(cos_loss, 0.0, 1.0)).detach()
        motion_strength_loss = motion_strength_loss * alpha
        motion_strength_loss = torch.mean(motion_strength_loss)

        return motion_strength_loss

    def compute_loss(self, image_before_nca, image_after_nca, nca_num_steps=1, return_summary=True):
        optic_flow, flow_img, flow_vector_field = self.get_opticflow(
            image_before_nca, image_after_nca,
            nca_num_steps=nca_num_steps,
            return_summary=return_summary
        )

        direction_loss = self.get_cosine_dist(optic_flow, self.target_motion_vec)
        direction_loss_value = direction_loss.item()
        motion_direction_loss = self.motion_direction_weight * direction_loss

        strength_loss = self.get_motion_strength_loss(
            optic_flow, self.target_motion_vec, nca_num_steps
        )
        strength_loss_value = strength_loss.item()
        motion_strength_loss = self.motion_strength_weight * strength_loss

        motion_loss = motion_direction_loss + motion_strength_loss
        motion_loss = self.motion_weight * motion_loss

        if return_summary:
            self.generated_video_flow = flow_img[None, ...]
            self.generated_flow_vector_field = flow_vector_field
            self.target_flow_vector_field = plot_vec_field(
                self.target_motion_vec[0].detach().cpu().numpy(),
                name='Target'
            )

        return motion_loss, direction_loss_value, strength_loss_value

    def save_visualizations(self, output_dir, epoch=None):
        os.makedirs(output_dir, exist_ok=True)

        if self.generated_video_flow is not None:
            generated_flow_vis = self.generated_video_flow / 255.0
            suffix = f"{epoch}" if epoch is not None else "final"
            save_train_image(
                generated_flow_vis[:4],
                f"{output_dir}/flow_gen_{suffix}.jpg"
            )

        if self.generated_flow_vector_field is not None:
            suffix = f"{epoch}" if epoch is not None else "final"
            self.generated_flow_vector_field.save(
                f"{output_dir}/vec_field_gen_{suffix}.png"
            )

        if self.target_flow_vector_field is not None:
            self.target_flow_vector_field.save(
                f"{output_dir}/vec_field_target.png"
            )

    def set_motion_weight(self, appearance_loss_log=None, new_weight=None):

        if new_weight is not None:
            self.motion_weight = new_weight
        elif appearance_loss_log is not None:
            median_appearance_loss = np.median(appearance_loss_log)
            self.motion_weight = median_appearance_loss / 50.0

            self.motion_direction_weight = 10.0 * (median_appearance_loss / 50.0)
            self.motion_strength_weight = 15.0 * (median_appearance_loss / 50.0)

        return self.motion_weight


