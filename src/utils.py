import torch
import io
import base64
import requests
import numpy as np
from PIL import Image
import torchvision.models as torch_models
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import matplotlib.pyplot as plt
import umap

from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import os
import numpy as np
import moviepy.editor as mvp
from moviepy.video.io.ffmpeg_writer import FFMPEG_VideoWriter
from tqdm import tqdm
import torchvision.transforms.functional as TF

from src.dynca_model.misc.flow_viz import flow_to_image, plot_vec_field
# os.environ['FFMPEG_BINARY'] = 'ffmpeg'

# import matplotlib.pyplot as pl
import gdown
import torch.nn.functional as F 

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')



class RelaxedOTLoss(torch.nn.Module):
    """https://arxiv.org/abs/1904.12785"""

    def __init__(self, vgg, n_samples=1024):
        super().__init__()
        self.n_samples = n_samples
        self.vgg = vgg

    @staticmethod
    def pairwise_distances_cos(x, y):
        x_norm = torch.norm(x, dim=2, keepdim=True)  # (b, n, 1)
        y_t = y.transpose(1, 2)  # (b, c, m) (m may be different from n)
        y_norm = torch.norm(y_t, dim=1, keepdim=True)  # (b, 1, m)
        # import pdb; pdb.set_trace();
        dist = 1. - torch.matmul(x, y_t) / (x_norm * y_norm + 1e-10)  # (b, n, m)
        return dist

    @staticmethod
    def style_loss(x, y):
        pairwise_distance = RelaxedOTLoss.pairwise_distances_cos(x, y)
        m1, m1_inds = pairwise_distance.min(1)
        m2, m2_inds = pairwise_distance.min(2)
        remd = torch.max(m1.mean(dim=1), m2.mean(dim=1))
        return remd

    @staticmethod
    def moment_loss(x, y):
        mu_x, mu_y = torch.mean(x, 1, keepdim=True), torch.mean(y, 1, keepdim=True)
        mu_diff = torch.abs(mu_x - mu_y).mean(dim=(1, 2))

        x_c, y_c = x - mu_x, y - mu_y
        x_cov = torch.matmul(x_c.transpose(1, 2), x_c) / (x.shape[1] - 1)
        y_cov = torch.matmul(y_c.transpose(1, 2), y_c) / (y.shape[1] - 1)

        cov_diff = torch.abs(x_cov - y_cov).mean(dim=(1, 2))
        return mu_diff + cov_diff

    def forward(self, target_images, generated_images):
        loss = 0.0
        with torch.no_grad():
            target_features = get_middle_feature_vgg(target_images, self.vgg, flatten=True)
        generated_features = get_middle_feature_vgg(generated_images, self.vgg, flatten=True)
        
        # Iterate over the VGG layers
        for x, y in zip(generated_features, target_features):
            # import pdb; pdb.set_trace()
            (b_x, c, n_x), (b_y, _, n_y) = x.shape, y.shape
            n_samples = min(n_x, n_y, self.n_samples)
            indices_x = torch.argsort(torch.rand(b_x, 1, n_x, device=x.device), dim=-1)[..., :n_samples]
            x = x.gather(-1, indices_x.expand(b_x, c, n_samples))
            indices_y = torch.argsort(torch.rand(b_y, 1, n_y, device=y.device), dim=-1)[..., :n_samples]
            y = y.gather(-1, indices_y.expand(b_y, c, n_samples))
            x, y = x.transpose(1, 2), y.transpose(1, 2)  # (b, n_samples, c)
            loss += self.style_loss(x, y) + self.moment_loss(x, y)

        return loss



def get_middle_feature_vgg(imgs, vgg_model, flatten=False, include_image_as_feat=False):
    style_layers = [1, 6, 11, 18, 25]  # 1, 6, 11, 18, 25
    mean = torch.tensor([0.485, 0.456, 0.406], device=imgs.device)[:, None, None]
    std = torch.tensor([0.229, 0.224, 0.225], device=imgs.device)[:, None, None]
    x = (imgs - mean) / std
    b, c, h, w = x.shape
    if include_image_as_feat:
        features = [x.reshape(b, c, h * w)]
    else:
        features = []
    for i, layer in enumerate(vgg_model[:max(style_layers) + 1]):
        x = layer(x)
        if i in style_layers:
            b, c, h, w = x.shape
            if flatten:
                features.append(x.reshape(b, c, h * w))
            else:
                features.append(x)
    return features


class TextureLoss(torch.nn.Module):
    def __init__(self, loss_type, **kwargs):
        """
        :param loss_type: 'OT', 'SlW', 'Gram'
        OT: Relaxed Optimal Transport Style loss proposed by Kolkin et al. https://arxiv.org/abs/1904.12785
        SlW: Sliced Wasserstein Style loss proposed by Heitz et al. https://arxiv.org/abs/2006.07229
        Gram: Gram Style loss proposed by Gatys et al. in https://arxiv.org/abs/1505.07376
        """
        super(TextureLoss, self).__init__()

        self.ot_weight = 0.
        self.slw_weight = 0.
        self.gram_weight = 0.

        self.loss_type = loss_type

        if loss_type == 'OT':
            self.ot_weight = 1.0
        elif loss_type == 'SlW':
            self.slw_weight = 1.0
        elif loss_type == 'Gram':
            self.gram_weight = 1.0

        self.device = kwargs['device']
        self.vgg = torch_models.vgg16(weights='IMAGENET1K_V1').features.to(self.device)
        self._create_losses()

    def _create_losses(self):
        self.loss_mapper = {}
        self.loss_weights = {}
        if self.slw_weight != 0:
            self.loss_mapper["SlW"] = SlicedWassersteinLoss(self.vgg)
            self.loss_weights["SlW"] = self.slw_weight

        if self.ot_weight != 0:
            self.loss_mapper["OT"] = RelaxedOTLoss(self.vgg)
            self.loss_weights["OT"] = self.ot_weight

        if self.gram_weight != 0:
            self.loss_mapper["Gram"] = GramLoss(self.vgg)
            self.loss_weights["Gram"] = self.gram_weight

    def forward(self, target_images, generated_images):
        loss = 0.0
        b, c, h, w = generated_images.shape
        _, _, ht, wt = target_images.shape
        if h != ht or w != wt:
            target_images = TF.resize(target_images, size=[h, w])
        for loss_name in self.loss_mapper:
            loss_weight = self.loss_weights[loss_name]
            loss_func = self.loss_mapper[loss_name]
            loss_per_image = loss_func(target_images, generated_images)
            loss += loss_weight * sum(loss_per_image) / len(loss_per_image)

        return loss, loss_per_image



class GramLoss(torch.nn.Module):
    def __init__(self, vgg):
        super(GramLoss, self).__init__()
        self.vgg = vgg

    @staticmethod
    def get_gram(y):
        b, c, h, w = y.size()
        features = y.view(b, c, w * h)
        features_t = features.transpose(1, 2)
        grams = features.bmm(features_t) / (h * w)
        return grams

    def forward(self, target_images, generated_images):
        with torch.no_grad():
            target_features = get_middle_feature_vgg(target_images, self.vgg)
        generated_features = get_middle_feature_vgg(generated_images, self.vgg)

        losses = []
        for target_feature, generated_feature in zip(target_features, generated_features):
            gram_target = self.get_gram(target_feature)
            gram_generated = self.get_gram(generated_feature)
            losses.append((gram_target - gram_generated).square().mean())
        return losses


class SlicedWassersteinLoss(torch.nn.Module):
    def __init__(self, vgg):
        super(SlicedWassersteinLoss, self).__init__()
        self.vgg = vgg

    @staticmethod
    def project_sort(x, proj):
        return torch.einsum('bcn,cp->bpn', x, proj).sort()[0]

    @staticmethod
    def sliced_wass_loss(source, target, proj_n=32):
        ch, n = source.shape[-2:]
        projs = F.normalize(torch.randn(ch, proj_n, device=source.device), dim=0)
        source_proj = SlicedWassersteinLoss.project_sort(source, projs)
        target_proj = SlicedWassersteinLoss.project_sort(target, projs)
        target_interp = F.interpolate(target_proj, n, mode='nearest')
        return (source_proj - target_interp).square().sum()

    def forward(self, target_images, generated_images, mask=None):
        with torch.no_grad():
            target_features = get_middle_feature_vgg(target_images, self.vgg, flatten=True,
                                                     include_image_as_feat=True)
        generated_features = get_middle_feature_vgg(generated_images, self.vgg, flatten=True,
                                                    include_image_as_feat=True)

        losses = [self.sliced_wass_loss(x, y) for x, y in zip(generated_features, target_features)]
        return losses
    

def imread(url, max_size=None, mode=None):
    if url.startswith(('http:', 'https:')):
        # wikimedia requires a user agent
        headers = {
            "User-Agent": "Requests in Colab/0.0 (https://colab.research.google.com/; <EMAIL>) requests/0.0"
        }
        r = requests.get(url, headers=headers)
        f = io.BytesIO(r.content)
    else:
        f = url
    img = Image.open(f)
    if max_size is not None:
        img.thumbnail((max_size, max_size), Image.LANCZOS)  # preserves aspect ratio
    if mode is not None:
        img = img.convert(mode)
    img = np.float32(img) / 255.0
    return img


def np2pil(a):
    if a.dtype in [np.float32, np.float64]:
        a = np.uint8(np.clip(a, 0, 1) * 255)
    return Image.fromarray(a)


def imwrite(f, a, fmt=None):
    a = np.asarray(a)
    if isinstance(f, str):
        fmt = f.rsplit('.', 1)[-1].lower()
        if fmt == 'jpg':
            fmt = 'jpeg'
        f = open(f, 'wb')
    np2pil(a).save(f, fmt, quality=95)


def imencode(a, fmt='jpeg'):
    a = np.asarray(a)
    if len(a.shape) == 3 and a.shape[-1] == 4:
        fmt = 'png'
    f = io.BytesIO()
    imwrite(f, a, fmt)
    return f.getvalue()


def im2url(a, fmt='jpeg'):
    encoded = imencode(a, fmt)
    base64_byte_string = base64.b64encode(encoded).decode('ascii')
    return 'data:image/' + fmt.upper() + ';base64,' + base64_byte_string


def imshow(a, fmt='jpeg', id=None):
    return display(Image(data=imencode(a, fmt)), display_id=id)


def zoom(img, scale=4):
    img = np.repeat(img, scale, 0)
    img = np.repeat(img, scale, 1)
    return img

def mask_img_name(img_token, name_token, mask_prob=0.75):
    img_mask = (torch.rand(img_token.size(1)) > mask_prob).float()
    img_mask = img_mask.unsqueeze(-1).expand_as(img_token)
    img_token = img_token * img_mask
    name_mask = (torch.rand(name_token.size(1)) > mask_prob).float()
    name_mask = name_mask.unsqueeze(-1).expand_as(name_token)
    name_token = name_token * name_mask

    null_token = torch.zeros(img_token.size(0), 1, img_token.size(-1))
    target_tokens = torch.cat([null_token, img_token, name_token, null_token], dim=1)
    return target_tokens

def mask_to_predict(img_token, name_token, mask_img=1):
    if mask_img:
        img_mask = torch.zeros_like(img_token)
        name_mask = torch.ones_like(name_token)
    else:
        name_mask = torch.zeros_like(name_token)
        img_mask = torch.ones_like(img_token)
    img_token = img_token * img_mask
    name_token = name_token * name_mask

    null_token = torch.zeros(img_token.size(0), 1, img_token.size(-1))
    target_tokens = torch.cat([null_token, img_token, name_token, null_token], dim=1)
    return target_tokens

class VideoWriter:
    def __init__(self, filename='tmp.mp4', fps=30.0, autoplay=False, **kw):
        self.writer = None
        self.autoplay = autoplay
        self.params = dict(filename=filename, fps=fps, **kw)

    def add(self, img):
        img = np.asarray(img)
        if self.writer is None:
            h, w = img.shape[:2]
            self.writer = FFMPEG_VideoWriter(size=(w, h), **self.params)
        if img.dtype in [np.float32, np.float64]:
            img = np.uint8(img.clip(0, 1) * 255)
        if len(img.shape) == 2:
            img = np.repeat(img[..., None], 3, -1)
        self.writer.write_frame(img)

    def close(self):
        if self.writer:
            self.writer.close()

    def __enter__(self):
        return self

    def __exit__(self, *kw):
        self.close()
        if self.autoplay:
            self.show()

    def show(self, **kw):
        self.close()
        fn = self.params['filename']
        display(mvp.ipython_display(fn, **kw))


  
class ClipEmbeddingDataset(Dataset):
    def __init__(self, csv_path, texture_names=None):
        """
        Args:
            csv_path (str): Path to the CSV file containing CLIP embeddings
            texture_names (list, optional): List of specific texture names to load
        """
        self.df = pd.read_csv(csv_path)
        self.texture_names = [name.replace('.tif', '') for name in self.df.columns.tolist()]
        
        # embeddings (768 x 7181)
        self.embeddings = self.df.values.T  # 转置以获得正确的维度 (7181 x 768)
        print(f"Embeddings shape: {self.embeddings.shape}")  # [7181, 768]
        
        if texture_names is not None:
            mask = [name in texture_names for name in self.texture_names]
            self.embeddings = self.embeddings[mask]
            self.texture_names = [name for name, m in zip(self.texture_names, mask) if m]
            
        self.unique_textures = np.array(list(set(self.texture_names)))
        self.texture_to_idx = {name: idx for idx, name in enumerate(self.unique_textures)}
        
    def __len__(self):
        return len(self.embeddings)
    
    def __getitem__(self, idx):
        embedding = torch.FloatTensor(self.embeddings[idx])
        texture_name = self.texture_names[idx]
        texture_idx = self.texture_to_idx[texture_name]
        return embedding, texture_name, texture_idx
    
    def get_embeddings_by_texture(self, texture_name):
        indices = [i for i, name in enumerate(self.texture_names) if name == texture_name]
        return torch.FloatTensor(self.embeddings[indices])
    
    def get_all_texture_names(self):
        return list(self.unique_textures)
    
    @property
    def embedding_dim(self):
        return self.embeddings.shape[1]


def plot_latent_projections(z_mean, texture_names, epoch, output_dir, method='umap'):

    plt.figure(figsize=(15, 15))
    
    categories = list(set(name[:3] for name in texture_names))
    categories.sort()  # 排序以保持颜色一致性
    
    markers = ['o', 's', '^', 'D', 'v', '<', '>', 'p', 'h', '8']
    while len(markers) < len(categories):
        markers.extend(markers)
    
    if method.lower() == 'umap':
        reducer = umap.UMAP(random_state=42)
        z_embedded = reducer.fit_transform(z_mean)
        title = f'Latent Space UMAP - All Samples (Epoch {epoch})'
    else:  # PCA
        reducer = PCA(n_components=2)
        z_embedded = reducer.fit_transform(z_mean)
        title = f'Latent Space PCA - All Samples (Epoch {epoch})'
    
    for idx, category in enumerate(categories):
        mask = [name[:3] == category for name in texture_names]
        mask = np.array(mask)
        if np.any(mask):  # 如果该类别有数据点
            plt.scatter(z_embedded[mask, 0], 
                       z_embedded[mask, 1],
                       marker=markers[idx],
                       label=category,
                       alpha=0.7)
    
    # for i, txt in enumerate(texture_names):
    #     plt.annotate(txt, (z_embedded[i, 0], z_embedded[i, 1]), 
    #                 fontsize=8,
    #                 alpha=0.7)
    
    for i, txt in enumerate(texture_names):
        plt.annotate('', (z_embedded[i, 0], z_embedded[i, 1]), 
                    fontsize=8,
                    alpha=0.7)
    
    plt.title(title)
    plt.xlabel(f'{method.upper()} 1')
    plt.ylabel(f'{method.upper()} 2')
    plt.legend(title='Categories', bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.tight_layout()
    
    save_dir = os.path.join(output_dir, f'zog_{method.lower()}_plots')
    os.makedirs(save_dir, exist_ok=True)
    plt.savefig(os.path.join(save_dir, f'latent_{method.lower()}_epoch_{epoch}.png'),
                bbox_inches='tight',
                dpi=300)
    plt.close()


def plot_latent_projections_new(z_mean, texture_names, epoch, output_dir, method='umap'):
    plt.figure(figsize=(15, 15))
    
    z_mean = np.array(z_mean, dtype=np.float32)
    
    print(f"Initial data shape: {z_mean.shape}")
    print(f"Initial data range: [{z_mean.min()}, {z_mean.max()}]")
    
    valid_indices = ~np.any(np.isnan(z_mean) | np.isinf(z_mean), axis=1)
    z_mean = z_mean[valid_indices]
    texture_names = [name for i, name in enumerate(texture_names) if valid_indices[i]]
    
    scaler = StandardScaler()
    z_mean = scaler.fit_transform(z_mean)
    
    z_mean = np.clip(z_mean, -10, 10)
    
    print(f"Processed data shape: {z_mean.shape}")
    print(f"Processed data range: [{z_mean.min()}, {z_mean.max()}]")
    
    if method.lower() == 'umap':
        reducer = umap.UMAP(random_state=42)
        z_embedded = reducer.fit_transform(z_mean)
        title = f'Latent Space UMAP - All Samples (Epoch {epoch})'
    else:  # PCA
        reducer = PCA(n_components=2)
        z_embedded = reducer.fit_transform(z_mean)
        title = f'Latent Space PCA - All Samples (Epoch {epoch})'
    
    categories = list(set(name[:3] for name in texture_names))
    categories.sort()
    
    markers = ['o', 's', '^', 'D', 'v', '<', '>', 'p', 'h', '8']
    while len(markers) < len(categories):
        markers.extend(markers)
    
    for idx, category in enumerate(categories):
        mask = [name[:3] == category for name in texture_names]
        mask = np.array(mask)
        if np.any(mask):
            plt.scatter(z_embedded[mask, 0], 
                        z_embedded[mask, 1],
                        marker=markers[idx],
                        label=category,
                        # s=10, # marker size
                        alpha=0.7)
    
    for i, txt in enumerate(texture_names):
        plt.annotate('', (z_embedded[i, 0], z_embedded[i, 1]), fontsize=8)
    
    plt.title(title)
    plt.xlabel(f'{method.upper()} 1')
    plt.ylabel(f'{method.upper()} 2')
    plt.legend(title='Categories', bbox_to_anchor=(1.05, 1), loc='upper left', markerscale=1.5)
    plt.tight_layout()
    
    save_dir = os.path.join(output_dir, f'zog_{method.lower()}_plots')
    os.makedirs(save_dir, exist_ok=True)
    plt.savefig(os.path.join(save_dir, f'latent_{method.lower()}_epoch_{epoch}.png'),
                bbox_inches='tight',
                dpi=300)
    
def plot_training_curves(losses_dict, epoch, output_dir):
    """
    Plot training curves for different losses
    
    Args:
        losses_dict: Dictionary containing lists of different losses
        epoch: Current epoch number
        output_dir: Directory to save the plot
    """
    plt.figure(figsize=(12, 8))
    for loss_name, loss_values in losses_dict.items():
        plt.plot(loss_values, label=loss_name)
    
    plt.title(f'Training Losses (Epoch {epoch})')
    plt.xlabel('Iterations')
    plt.ylabel('Loss Value')
    plt.legend()
    plt.grid(True)
    
    # Save plot
    save_dir = os.path.join(output_dir, 'training_curves')
    os.makedirs(save_dir, exist_ok=True)
    plt.savefig(os.path.join(save_dir, f'losses_epoch_{epoch}.png'))
    plt.close()


def save_video(state, output_dir, video_name, nca_model, video_length, size_factor=1.0, step_n=8):
    fps = 30
    with VideoWriter(filename=f"{output_dir}/{video_name}.mp4", fps=fps, autoplay=False) as vid, torch.no_grad():
        for k in tqdm(range(int(video_length * fps)), desc="Making the video..."):
            states = nca_model.decode(state, step_n)

            state = states[-1]
            z = nca_model.to_rgb(state)
            
            img = z.detach().cpu().numpy()[0]
            img = img.transpose(1, 2, 0)

            img = np.clip(img, -1.0, 1.0)
            img = (img + 1.0) / 2.0

            vid.add(img)

def load_weights(model, weights_path):
    """Load weights from a .pth file"""
    try:
        if os.path.exists(weights_path):
            state_dict = torch.load(weights_path, map_location=device)
            
            current_state_dict = model.state_dict()
            
            new_state_dict = {}
            for key in current_state_dict.keys():
                if key.startswith('model.'):
                    stripped_key = key.replace('model.', '')
                    if stripped_key in state_dict:
                        new_state_dict[key] = state_dict[stripped_key]
                else:
                    prefixed_key = f'model.{key}'
                    if prefixed_key in state_dict:
                        new_state_dict[key] = state_dict[prefixed_key]
                    elif key in state_dict:
                        new_state_dict[key] = state_dict[key]
            
            model.load_state_dict(new_state_dict)
            print(f"Model weights loaded successfully from {weights_path}")
            return model
        else:
            print(f"Weights file not found: {weights_path}")
            return model
    except Exception as e:
        print(f"Error loading model weights: {e}")
        return model


class RandomPatchReplacer:
    def __init__(self, min_percent, max_percent):
        self.min_percent = min_percent
        self.max_percent = max_percent
    
    def apply(self, data, mask=None):
        result = data.clone()
        batch_size, channels, height, width = data.shape
        
        for b in range(batch_size):
            # 10% - 30% replace
            h_percent = torch.rand(1).item() * (self.max_percent - self.min_percent) + self.min_percent
            w_percent = torch.rand(1).item() * (self.max_percent - self.min_percent) + self.min_percent
            patch_height = int(height * h_percent)
            patch_width = int(width * w_percent)
            
            # left top point
            start_h = torch.randint(0, height - patch_height + 1, (1,)).item()
            start_w = torch.randint(0, width - patch_width + 1, (1,)).item()
            
            if mask is None:
                patch = (torch.rand(channels, patch_height, patch_width, device=data.device) - 0.5) * 2.0
            else:
                patch = mask[b, :, start_h:start_h+patch_height, start_w:start_w+patch_width]
            
            result[b, :, start_h:start_h+patch_height, start_w:start_w+patch_width] = patch
        
        return result
    

    
def pool_genome(texture_name, pools):
    if texture_name[:3] == 'ADI':
        pools[texture_name][:, -1:, :, :] = 1
        pools[texture_name][:, -2:-1, :, :] = 0
        pools[texture_name][:, -3:-2, :, :] = 0
        pools[texture_name][:, -4:-3, :, :] = 0
        pools[texture_name][:, -5:-4, :, :] = 0
        pools[texture_name][:, -6:-5, :, :] = 0
        pools[texture_name][:, -7:-6, :, :] = 0
        pools[texture_name][:, -8:-7, :, :] = 0
        pools[texture_name][:, -9:-8, :, :] = 0
    elif texture_name[:3] == 'BAC':
        pools[texture_name][:, -1:, :, :] = 0
        pools[texture_name][:, -2:-1, :, :] = 1
        pools[texture_name][:, -3:-2, :, :] = 0
        pools[texture_name][:, -4:-3, :, :] = 0
        pools[texture_name][:, -5:-4, :, :] = 0
        pools[texture_name][:, -6:-5, :, :] = 0
        pools[texture_name][:, -7:-6, :, :] = 0
        pools[texture_name][:, -8:-7, :, :] = 0
        pools[texture_name][:, -9:-8, :, :] = 0
    elif texture_name[:3] == 'DEB':
        pools[texture_name][:, -1:, :, :] = 0
        pools[texture_name][:, -2:-1, :, :] = 0
        pools[texture_name][:, -3:-2, :, :] = 1
        pools[texture_name][:, -4:-3, :, :] = 0
        pools[texture_name][:, -5:-4, :, :] = 0
        pools[texture_name][:, -6:-5, :, :] = 0
        pools[texture_name][:, -7:-6, :, :] = 0
        pools[texture_name][:, -8:-7, :, :] = 0
        pools[texture_name][:, -9:-8, :, :] = 0
    elif texture_name[:3] == 'LYM':
        pools[texture_name][:, -1:, :, :] = 0
        pools[texture_name][:, -2:-1, :, :] = 0
        pools[texture_name][:, -3:-2, :, :] = 0
        pools[texture_name][:, -4:-3, :, :] = 1
        pools[texture_name][:, -5:-4, :, :] = 0
        pools[texture_name][:, -6:-5, :, :] = 0
        pools[texture_name][:, -7:-6, :, :] = 0
        pools[texture_name][:, -8:-7, :, :] = 0
        pools[texture_name][:, -9:-8, :, :] = 0
    elif texture_name[:3] == 'MUC':
        pools[texture_name][:, -1:, :, :] = 0
        pools[texture_name][:, -2:-1, :, :] = 0
        pools[texture_name][:, -3:-2, :, :] = 0
        pools[texture_name][:, -4:-3, :, :] = 0
        pools[texture_name][:, -5:-4, :, :] = 1
        pools[texture_name][:, -6:-5, :, :] = 0
        pools[texture_name][:, -7:-6, :, :] = 0
        pools[texture_name][:, -8:-7, :, :] = 0
        pools[texture_name][:, -9:-8, :, :] = 0
    elif texture_name[:3] == 'MUS':
        pools[texture_name][:, -1:, :, :] = 0
        pools[texture_name][:, -2:-1, :, :] = 0
        pools[texture_name][:, -3:-2, :, :] = 0
        pools[texture_name][:, -4:-3, :, :] = 0
        pools[texture_name][:, -5:-4, :, :] = 0
        pools[texture_name][:, -6:-5, :, :] = 1
        pools[texture_name][:, -7:-6, :, :] = 0
        pools[texture_name][:, -8:-7, :, :] = 0
        pools[texture_name][:, -9:-8, :, :] = 0
    elif texture_name[:3] == 'NOR':
        pools[texture_name][:, -1:, :, :] = 0
        pools[texture_name][:, -2:-1, :, :] = 0
        pools[texture_name][:, -3:-2, :, :] = 0
        pools[texture_name][:, -4:-3, :, :] = 0
        pools[texture_name][:, -5:-4, :, :] = 0
        pools[texture_name][:, -6:-5, :, :] = 0
        pools[texture_name][:, -7:-6, :, :] = 1
        pools[texture_name][:, -8:-7, :, :] = 0
        pools[texture_name][:, -9:-8, :, :] = 0
    elif texture_name[:3] == 'STR':
        pools[texture_name][:, -1:, :, :] = 0
        pools[texture_name][:, -2:-1, :, :] = 0
        pools[texture_name][:, -3:-2, :, :] = 0
        pools[texture_name][:, -4:-3, :, :] = 0
        pools[texture_name][:, -5:-4, :, :] = 0
        pools[texture_name][:, -6:-5, :, :] = 0
        pools[texture_name][:, -7:-6, :, :] = 0
        pools[texture_name][:, -8:-7, :, :] = 1
        pools[texture_name][:, -9:-8, :, :] = 0
    elif texture_name[:3] == 'TUM':
        pools[texture_name][:, -1:, :, :] = 0
        pools[texture_name][:, -2:-1, :, :] = 0
        pools[texture_name][:, -3:-2, :, :] = 0
        pools[texture_name][:, -4:-3, :, :] = 0
        pools[texture_name][:, -5:-4, :, :] = 0
        pools[texture_name][:, -6:-5, :, :] = 0
        pools[texture_name][:, -7:-6, :, :] = 0
        pools[texture_name][:, -8:-7, :, :] = 0
        pools[texture_name][:, -9:-8, :, :] = 1
    return pools[texture_name]

def one_genome(texture_name, x):
    if texture_name[:3] == 'ADI':
        x[:, -1:, :, :] = 1
        x[:, -2:-1, :, :] = 0
        x[:, -3:-2, :, :] = 0
        x[:, -4:-3, :, :] = 0
        x[:, -5:-4, :, :] = 0
        x[:, -6:-5, :, :] = 0
        x[:, -7:-6, :, :] = 0
        x[:, -8:-7, :, :] = 0
        x[:, -9:-8, :, :] = 0
    elif texture_name[:3] == 'BAC':
        x[:, -1:, :, :] = 0
        x[:, -2:-1, :, :] = 1
        x[:, -3:-2, :, :] = 0
        x[:, -4:-3, :, :] = 0
        x[:, -5:-4, :, :] = 0
        x[:, -6:-5, :, :] = 0
        x[:, -7:-6, :, :] = 0
        x[:, -8:-7, :, :] = 0
        x[:, -9:-8, :, :] = 0
    elif texture_name[:3] == 'DEB':
        x[:, -1:, :, :] = 0
        x[:, -2:-1, :, :] = 0
        x[:, -3:-2, :, :] = 1
        x[:, -4:-3, :, :] = 0
        x[:, -5:-4, :, :] = 0
        x[:, -6:-5, :, :] = 0
        x[:, -7:-6, :, :] = 0
        x[:, -8:-7, :, :] = 0
        x[:, -9:-8, :, :] = 0
    elif texture_name[:3] == 'LYM':
        x[:, -1:, :, :] = 0
        x[:, -2:-1, :, :] = 0
        x[:, -3:-2, :, :] = 0
        x[:, -4:-3, :, :] = 1
        x[:, -5:-4, :, :] = 0
        x[:, -6:-5, :, :] = 0
        x[:, -7:-6, :, :] = 0
        x[:, -8:-7, :, :] = 0
        x[:, -9:-8, :, :] = 0
    elif texture_name[:3] == 'MUC':
        x[:, -1:, :, :] = 0
        x[:, -2:-1, :, :] = 0
        x[:, -3:-2, :, :] = 0
        x[:, -4:-3, :, :] = 0
        x[:, -5:-4, :, :] = 1
        x[:, -6:-5, :, :] = 0
        x[:, -7:-6, :, :] = 0
        x[:, -8:-7, :, :] = 0
        x[:, -9:-8, :, :] = 0
    elif texture_name[:3] == 'MUS':
        x[:, -1:, :, :] = 0
        x[:, -2:-1, :, :] = 0
        x[:, -3:-2, :, :] = 0
        x[:, -4:-3, :, :] = 0
        x[:, -5:-4, :, :] = 0
        x[:, -6:-5, :, :] = 1
        x[:, -7:-6, :, :] = 0
        x[:, -8:-7, :, :] = 0
        x[:, -9:-8, :, :] = 0
    elif texture_name[:3] == 'NOR':
        x[:, -1:, :, :] = 0
        x[:, -2:-1, :, :] = 0
        x[:, -3:-2, :, :] = 0
        x[:, -4:-3, :, :] = 0
        x[:, -5:-4, :, :] = 0
        x[:, -6:-5, :, :] = 0
        x[:, -7:-6, :, :] = 1
        x[:, -8:-7, :, :] = 0
        x[:, -9:-8, :, :] = 0
    elif texture_name[:3] == 'STR':
        x[:, -1:, :, :] = 0
        x[:, -2:-1, :, :] = 0
        x[:, -3:-2, :, :] = 0
        x[:, -4:-3, :, :] = 0
        x[:, -5:-4, :, :] = 0
        x[:, -6:-5, :, :] = 0
        x[:, -7:-6, :, :] = 0
        x[:, -8:-7, :, :] = 1
        x[:, -9:-8, :, :] = 0
    elif texture_name[:3] == 'TUM':
        x[:, -1:, :, :] = 0
        x[:, -2:-1, :, :] = 0
        x[:, -3:-2, :, :] = 0
        x[:, -4:-3, :, :] = 0
        x[:, -5:-4, :, :] = 0
        x[:, -6:-5, :, :] = 0
        x[:, -7:-6, :, :] = 0
        x[:, -8:-7, :, :] = 0
        x[:, -9:-8, :, :] = 1
    return x


def save_video(state, output_dir, video_name, nca_model, video_length=10, fps=30, step_n=1):
    os.makedirs(output_dir, exist_ok=True)
    with VideoWriter(filename=f"{output_dir}/{video_name}.mp4", fps=fps, autoplay=False) as vid, torch.no_grad():
        for k in tqdm(range(int(video_length * fps)), desc=f"video for {video_name}..."):
            
            if hasattr(nca_model, 'forward_nsteps'): # DyNCA
                state, rgb_state = nca_model.forward_nsteps(state, step_n, update_rate=0.5)
                img = rgb_state.permute([0, 2, 3, 1]).detach().cpu().numpy()
            else: # NoiseNCA
                for _ in range(step_n):
                    state = nca_model(state)
                img = nca_model.to_rgb(state).permute([0, 2, 3, 1]).detach().cpu().numpy()

            img = ((np.clip(img, -1, 1)+1)/2.0 * 255.0).astype(np.uint8).squeeze()
            vid.add(img)


def get_opticflow(image1, image2, motion_model, nca_base_num_steps=24, size=(128, 128), nca_num_steps=1, return_summary=False):
    image1_size = image1.shape[2]
    image2_size = image2.shape[2]
    if image1_size != size[0]:
        image1 = TF.resize(image1, size)
    if image2_size != size[0]:
        image2 = TF.resize(image2, size)

    # MSOEnet接受灰度图像 [0,1]
    x1 = (image1 + 1.0) / 2.0
    x2 = (image2 + 1.0) / 2.0
    x1 = TF.rgb_to_grayscale(x1)
    x2 = TF.rgb_to_grayscale(x2)
    image_cat = torch.stack([x1, x2], dim=-1)
    flow, _ = motion_model(image_cat, return_features=True)

    if return_summary:
        flow_img = flow_to_image(flow[0].permute(1, 2, 0).detach().cpu().numpy()).transpose(2, 0, 1)
        rescaled_flow = flow * nca_base_num_steps / nca_num_steps
        mean_rescaled_flow = torch.mean(rescaled_flow, dim=0)
        flow_vector_field = plot_vec_field(mean_rescaled_flow.detach().cpu().numpy(), name='Generated')
    else:
        flow_img = None
        flow_vector_field = None

    return flow, flow_img, flow_vector_field

# 计算运动强度损失
def get_motion_strength_loss(optic_flow, target_motion_vec, nca_base_num_steps, nca_num_steps=1):
    cos_sim = torch.nn.CosineSimilarity(dim=1)
    motion_strength = torch.norm(optic_flow, dim=1) * nca_base_num_steps / nca_num_steps
    target_strength = torch.norm(target_motion_vec, dim=1)
    motion_strength_loss = torch.abs(motion_strength - target_strength)

    direction_cos_sim = cos_sim(optic_flow, target_motion_vec)
    cos_loss = 1.0 - torch.mean(direction_cos_sim, dim=[1, 2], keepdim=True)

    alpha = (1.0 - torch.clip(cos_loss, 0.0, 1.0)).detach()
    motion_strength_loss = motion_strength_loss * alpha
    motion_strength_loss = torch.mean(motion_strength_loss)

    return motion_strength_loss

# 计算余弦距离损失
def get_cosine_dist(optic_flow, target_motion_vec):
    cos_sim = torch.nn.CosineSimilarity(dim=1)
    direction_cos_sim = cos_sim(optic_flow, target_motion_vec)
    direction_loss = 1.0 - torch.mean(direction_cos_sim)
    return direction_loss


def plot_train_log(log_dict, plt_number, save_path):
    plt.figure(figsize=(6 * plt_number, 6))
    plt_idx = 1
    for log_name in log_dict.keys():
        loss_log = log_dict[log_name][0]
        if(len(loss_log) == 0):
            continue
        y_scale = log_dict[log_name][1]
        ylim = log_dict[log_name][2]

        plt.subplot(1, plt_number, plt_idx)
        plt.plot(loss_log, '.', alpha=0.1)
        plt.title(log_name)
        if (y_scale):
            plt.yscale('log')
        if (ylim):
            plt.ylim(np.min(loss_log), max(loss_log))
        plt_idx += 1
    plt.savefig(save_path)


def print_model_parameters(model):
    for name, param in model.named_parameters():
        if param.requires_grad:
            print(f"{name:20} | Shape: {str(param.shape):15} | Params: {param.numel():,}")