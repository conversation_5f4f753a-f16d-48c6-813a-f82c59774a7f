import os
import torch
import numpy as np
import torchvision.transforms.functional as TF
import torch.nn.functional as F
from src.dynca_model.misc.flow_viz import flow_to_image, plot_vec_field
from src.dynca_model.vector_field_loss import get_motion_vector_field_by_name
from src.dynca_model.misc.display_utils import save_train_image
from torchvision import transforms
from PIL import Image, ImageSequence
import cv2
import random
import copy

from src.dynca_model.MSOEmultiscale import MSOEmultiscale


class VideoMotionLoss:
    def __init__(self,
                 motion_model,
                 video_motion_loss_type,
                 motion_weight,
                 motion_img_size=(128, 128),
                 img_size=(128, 128),
                 device='cuda'):
        
        model = MSOEmultiscale()
        states_dict = torch.load(f'src/dynca_model/{motion_model}_model.pth')
        model.load_state_dict(states_dict)
        self.motion_model = model.to(device).eval()
        self.motion_img_size = motion_img_size
        self.img_size = img_size
        self.device = device
        self.motion_weight = motion_weight

        if video_motion_loss_type == 'MotionOT':
            self.video_motion_loss = MotionSlicedWassersteinLoss()
        elif video_motion_loss_type == 'MotionSlW':
            self.video_motion_loss = MotionOptimalTransportLoss()
        elif video_motion_loss_type == 'MotionGram':
            self.video_motion_loss = MotionGramLoss()

    def get_motion_feature_two_frames(self, image1, image2, size=(128, 128)):
        image1 = image1.to(self.device)
        image2 = image2.to(self.device)
        image1_size = image1.shape[2]
        image2_size = image2.shape[2]
        if (image1_size != size[0]):
            image1 = TF.resize(image1, size)
        if (image2_size != size[0]):
            image2 = TF.resize(image2, size)
        
        x1 = (image1 + 1.0) / 2.0
        x2 = (image2 + 1.0) / 2.0
        x1 = TF.rgb_to_grayscale(x1)
        x2 = TF.rgb_to_grayscale(x2)
        image_cat = torch.stack([x1, x2], dim=-1)
        
        flow, motion_feature = self.motion_model(image_cat, return_features=True)

        return motion_feature, flow
        
    def get_train_image_seq(self, target_appearance_path, target_dynamics_path):
        if('.png' in target_appearance_path or '.jpg' in target_appearance_path or '.jpeg' in target_appearance_path):
            style_img = Image.open(target_appearance_path)
            train_image_seq_texture = self.preprocess_style_image(style_img, img_size=self.img_size)
            train_image_seq_texture = train_image_seq_texture[0:1].to(self.device) # 1, C, H, W
            train_image_seq_texture = (train_image_seq_texture * 2.0) - 1.0
            frame_idx_texture = 0
            train_image_texture = copy.deepcopy(train_image_seq_texture[frame_idx_texture])
            train_image_texture_save = transforms.ToPILImage()((train_image_texture + 1.0) / 2.0)
        else:
            train_image_seq_sort = self.preprocess_video(target_dynamics_path)
            train_image_seq_sort = train_image_seq_sort.permute(1, 0, 2, 3).to(self.device)
            video_length = len(train_image_seq_sort)
            frame_weight_list = []
            with torch.no_grad():
                for idx in range(video_length - 1):
                    image1 = train_image_seq_sort[idx:idx+1]
                    image2 = train_image_seq_sort[idx+1:idx+2]

                    _, flow = self.get_motion_feature_two_frames(image1, image2, size=self.img_size) ####
                    motion_strength = torch.mean(torch.norm(flow, dim = 1)).item()
                    frame_weight_list.append(motion_strength)
            total_strength = sum(frame_weight_list)
            frame_weight_list = [x / total_strength for x in frame_weight_list]
            train_image_seq_texture = self.preprocess_video(target_appearance_path)
            train_image_seq_texture = train_image_seq_texture.permute(1, 0, 2, 3).to(self.device) # T, C, H, W
            texture_video_length = len(train_image_seq_texture)
            frame_idx_texture = np.argmax(frame_weight_list)
            if(frame_idx_texture >= texture_video_length):
                frame_idx_texture = random.randint(0, texture_video_length - 1)
            train_image_texture = copy.deepcopy(train_image_seq_texture[frame_idx_texture])
            train_image_texture_save = transforms.ToPILImage()((train_image_texture + 1.0) / 2.0)

        return train_image_seq_texture, train_image_texture, train_image_texture_save, frame_idx_texture
  
    def preprocess_video(self, video_path, normalRGB = False):
        if ('.gif' in video_path):
            gif_video = Image.open(video_path)
            train_image_seq = []
            index = 0
            for frame in ImageSequence.Iterator(gif_video):
                cur_frame_tensor = self.preprocess_style_image(frame, self.img_size)
                if(normalRGB == False):
                    cur_frame_tensor = cur_frame_tensor * 2.0 - 1.0
                    
                train_image_seq.append(cur_frame_tensor)
                index += 1
            train_image_seq = torch.stack(train_image_seq, dim=2)[0] # Output shape is [C, T, H, W]
            # print(f'Total Training Frames: {index}')
            return train_image_seq
        elif('.avi' in video_path or '.mp4' in video_path):
            cap = cv2.VideoCapture(video_path)
            train_image_seq = []
            index = 0
            while cap.isOpened():
                ret, frame = cap.read()
                if not ret:
                    # print(f'Total Training Frames: {index}')
                    break
                index += 1
                # if(index == 50):
                #     break
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB) # BGR->RGB
                frame = Image.fromarray(frame.astype(np.uint8)).convert('RGB')

                cur_frame_tensor = self.preprocess_style_image(frame, self.img_size)
                if(normalRGB == False):
                    cur_frame_tensor = cur_frame_tensor * 2.0 - 1.0
                    
                train_image_seq.append(cur_frame_tensor)
            train_image_seq = torch.stack(train_image_seq, dim=2)[0]
            
            cap.release()
            cv2.destroyAllWindows()
            return train_image_seq
    
    def preprocess_style_image(self, style_img, img_size = [128, 128], batch_size = 4):
        w, h = style_img.size
        if(w == h):
            style_img = style_img.resize((img_size[0], img_size[1]))
            style_img = style_img.convert('RGB')
            # with torch.no_grad():
            #     style_img_tensor = transforms.ToTensor()(style_img).unsqueeze(0)
        else:
            style_img = style_img.convert('RGB')
            style_img = np.array(style_img)
            h, w, _ = style_img.shape
            cut_pixel = abs(w - h) // 2
            if(w > h):
                style_img = style_img[:, cut_pixel:w-cut_pixel, :]
            else:
                style_img = style_img[cut_pixel:h-cut_pixel, :, :]
            style_img = Image.fromarray(style_img.astype(np.uint8))
            style_img = style_img.resize((img_size[0],img_size[1]))
        style_img = np.float32(style_img) / 255.0
        style_img = torch.as_tensor(style_img)
        style_img = style_img[None,...]
        input_img_style = style_img.permute(0, 3, 1, 2)
        input_img_style = input_img_style.repeat(batch_size, 1, 1, 1)
        return input_img_style#, style_img_tensor
    
    def set_video_weight(self, loss_num=5, medium_mt=None):
        if not hasattr(self, 'args') or self.args is None:
            return loss_num
            
        img_size = self.args.img_size[0]
        motion_loss_weight_reset = loss_num
        
        if medium_mt is not None:
            if img_size == 256:
                motion_loss_weight_reset = min(10.0, max(medium_mt * 6.04 - 2.17, 2.0))
            elif img_size == 128:
                motion_loss_weight_reset = min(10.0, max(medium_mt * 5.82 - 1.05, 2.0))
        
        self.motion_weight = motion_loss_weight_reset

    def forward(self, generated_image_list, target_image_list, return_summary=True):
        assert len(generated_image_list) >= 2

        loss = 0.0
        # import pdb; pdb.set_trace()
        for idx in range(len(generated_image_list) - 1):
            generated_image_before = generated_image_list[idx]
            generated_image_after = generated_image_list[idx + 1]
            
            target_image_1 = target_image_list[idx]
            target_image_2 = target_image_list[idx + 1]
            
            motion_feature_gen_list, flow_gen = self.get_motion_feature_two_frames(generated_image_before, generated_image_after, size=self.motion_img_size)
            with torch.no_grad():
                motion_feature_target_list, flow_target = self.get_motion_feature_two_frames(target_image_1, target_image_2, size=self.motion_img_size)
            
            loss += self.video_motion_loss(motion_feature_target_list, motion_feature_gen_list)
            if return_summary:
                flow_gen_list = [flow_to_image(flow_gen[b].permute(1, 2, 0).detach().cpu().numpy()).transpose(2, 0, 1) 
                                for b in range(len(flow_gen))]
                flow_target_list = [flow_to_image(flow_target[b].permute(1, 2, 0).detach().cpu().numpy()).transpose(2, 0, 1) 
                                for b in range(len(flow_target))]
                
                flow_gen_numpy = np.stack(flow_gen_list)
                flow_target_numpy = np.stack(flow_target_list)
                
                vec_gen_list = [np.array(plot_vec_field(flow_gen[b].detach().cpu().numpy(), name=f'Generated{torch.mean(torch.norm(flow_gen[b], dim=0)).item()}')).transpose(2, 0, 1) for b in range(len(flow_gen))]
                vec_target_list = [np.array(plot_vec_field(flow_target[b].detach().cpu().numpy(), name=f'Target{torch.mean(torch.norm(flow_target[b], dim=0)).item()}')).transpose(2, 0, 1) for b in range(len(flow_target))]            
                vec_gen_numpy = np.stack(vec_gen_list)
                vec_target_numpy = np.stack(vec_target_list)
                
                summary = {}
                summary['target_video_flow'] = flow_target_numpy
                summary['generated_video_flow'] = flow_gen_numpy
                summary['target_video_vec'] = vec_target_numpy
                summary['generated_video_vec'] = vec_gen_numpy
                return loss, summary
            else:
                return loss, None
    
    

class MotionSlicedWassersteinLoss(torch.nn.Module):
    def __init__(self):
        super(MotionSlicedWassersteinLoss, self).__init__()
        self.device = 'cuda'

    @staticmethod
    def project_sort(x, proj):
        return torch.einsum('bcn,cp->bpn', x, proj).sort()[0]

    def sliced_ot_loss(self, source, target, proj_n=32):
        ch, n = source.shape[-2:]
        projs = F.normalize(torch.randn(ch, proj_n), dim=0).to(self.device)
        source_proj = MotionSlicedWassersteinLoss.project_sort(source, projs)
        target_proj = MotionSlicedWassersteinLoss.project_sort(target, projs)
        target_interp = F.interpolate(target_proj, n, mode='nearest')
        return (source_proj - target_interp).square().sum()

    def forward(self, target_features, generated_features):
        loss = 0.0
        for x, y in zip(generated_features, target_features):
            b,c,h,w = x.shape
            x = x.reshape(b,c,h*w)
            y = y.reshape(b,c,h*w)
            loss += self.sliced_ot_loss(x, y)
        return loss
        
        
class MotionOptimalTransportLoss(torch.nn.Module):
    def __init__(self):
        super(MotionOptimalTransportLoss, self).__init__()

    @staticmethod
    def pairwise_distances_cos(x, y):
        x_norm = torch.sqrt((x ** 2).sum(1).view(-1, 1))
        y_t = torch.transpose(y, 0, 1)
        y_norm = torch.sqrt((y ** 2).sum(1).view(1, -1))
        dist = 1. - torch.mm(x, y_t) / (x_norm + 1e-10) / (y_norm + 1e-10)
        return dist

    @staticmethod
    def style_loss_cos(X, Y, cos_d=True):
        # X,Y: 1*d*N*1
        d = X.shape[1]

        X = X.transpose(0, 1).contiguous().view(d, -1).transpose(0, 1)  # N*d
        Y = Y.transpose(0, 1).contiguous().view(d, -1).transpose(0, 1)

        # Relaxed EMD
        CX_M = MotionOptimalTransportLoss.pairwise_distances_cos(X, Y)

        m1, m1_inds = CX_M.min(1)
        m2, m2_inds = CX_M.min(0)

        remd = torch.max(m1.mean(), m2.mean())

        return remd

    @staticmethod
    def moment_loss(X, Y):  # matching mean and cov
        X = X.squeeze().t()
        Y = Y.squeeze().t()

        mu_x = torch.mean(X, 0, keepdim=True)
        mu_y = torch.mean(Y, 0, keepdim=True)
        mu_d = torch.abs(mu_x - mu_y).mean()

        X_c = X - mu_x
        Y_c = Y - mu_y
        X_cov = torch.mm(X_c.t(), X_c) / (X.shape[0] - 1)
        Y_cov = torch.mm(Y_c.t(), Y_c) / (Y.shape[0] - 1)

        D_cov = torch.abs(X_cov - Y_cov).mean()
        loss = mu_d + D_cov

        return loss

    @staticmethod
    def get_ot_loss_single_batch(x_feature, y_feature):
        randomize = True
        loss = 0
        for x, y in zip(x_feature, y_feature):
            c = x.shape[1]
            h, w = x.shape[2], x.shape[3]
            x = x.reshape(1, c, -1, 1)
            y = y.reshape(1, c, -1, 1)
            if h > 32 and randomize:
                indices = np.random.choice(np.arange(h * w), size=1000, replace=False)
                indices = np.sort(indices)
                indices = torch.LongTensor(indices)
                x = x[:, :, indices, :]
                y = y[:, :, indices, :]
            loss += MotionOptimalTransportLoss.style_loss_cos(x, y)
            loss += MotionOptimalTransportLoss.moment_loss(x, y)
        return loss

    def forward(self, target_features, generated_features):
        batch_size = target_features[0].shape[0]
        loss = 0.0
        for b in range(batch_size):
            target_feature = [t[b:b + 1] for t in target_features]
            generated_feature = [g[b:b + 1] for g in generated_features]
            loss += self.get_ot_loss_single_batch(target_feature, generated_feature)
        return loss / batch_size
    
class MotionGramLoss(torch.nn.Module):
    def __init__(self):
        super(MotionGramLoss, self).__init__()

    @staticmethod
    def get_gram(y):
        b, c, h, w = y.size()
        features = y.view(b, c, h * w)
        G = torch.bmm(features, features.transpose(1, 2))
        return G.div(h * w)
