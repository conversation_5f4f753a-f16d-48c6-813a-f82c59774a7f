import os
import numpy as np
import moviepy.editor as mvp
from moviepy.video.io.ffmpeg_writer import FFMPEG_VideoWriter
from tqdm import tqdm
import torch
from src.utils import one_genome
import torchvision.transforms.functional as TF


os.environ['FFMPEG_BINARY'] = 'ffmpeg'


class VideoWriter:
    def __init__(self, filename='tmp.mp4', fps=30.0, autoplay=False, **kw):
        self.writer = None
        self.autoplay = autoplay
        self.params = dict(filename=filename, fps=fps, **kw)

    def add(self, img):
        img = np.asarray(img)
        if self.writer is None:
            h, w = img.shape[:2]
            self.writer = FFMPEG_VideoWriter(size=(w, h), **self.params)
        if img.dtype in [np.float32, np.float64]:
            img = np.uint8(img.clip(-1, 1) +1)/2 * 255
        if len(img.shape) == 2:
            img = np.repeat(img[..., None], 3, -1)
        self.writer.write_frame(img)

    def close(self):
        if self.writer:
            self.writer.close()

    def __enter__(self):
        return self

    def __exit__(self, *kw):
        self.close()
        if self.autoplay:
            self.show()

    def show(self, **kw):
        self.close()
        fn = self.params['filename']
        display(mvp.ipython_display(fn, **kw))

def save_video(state, output_dir, video_name, nca_model, video_length=10, fps=30, step_n=1):
    os.makedirs(output_dir, exist_ok=True)
    with VideoWriter(filename=f"{output_dir}/{video_name}.mp4", fps=fps, autoplay=False) as vid, torch.no_grad():
        for k in tqdm(range(int(video_length * fps)), desc=f"video for {video_name}..."):
            
            if hasattr(nca_model, 'forward_nsteps'): # DyNCA
                state, rgb_state = nca_model.forward_nsteps(state, step_n, update_rate=0.5)
                img = rgb_state.permute([0, 2, 3, 1]).detach().cpu().numpy()
            else: # NoiseNCA
                for _ in range(step_n):
                    state = nca_model(state)
                img = nca_model.to_rgb(state).permute([0, 2, 3, 1]).detach().cpu().numpy()

            img = ((np.clip(img, -1, 1)+1)/2.0 * 255.0).astype(np.uint8).squeeze()
            vid.add(img)

def synthesize_video(nca_model, texture_name, video_length, output_dir, target_image_seq_texture, target_image_seq,
                     video_name='video', nca_step=32, record_loss=False, loss_class=None, seed_size=[128, 128], fps=25):
    motion_video_length, texture_video_length = len(target_image_seq), len(target_image_seq_texture)
    with VideoWriter(filename=f"{output_dir}/{video_name}.mp4", fps=fps, autoplay=False) as vid, torch.no_grad():
        h = nca_model.seed(1, seed_size[0], seed_size[1])
        h = one_genome(texture_name, h)
        if (record_loss):
            assert loss_class is not None
            prev_z = None
            total_video_motion_loss_avg = 0.0
            total_appearance_loss_avg = 0.0
        for k in tqdm(range(int(video_length)), desc="Making the video..."):
            step_n = nca_step
            nca_state, nca_feature = nca_model.forward_nsteps(h, step_n)

            z = nca_feature

            if (record_loss):
                cur_video_motion_loss_avg = 0.0
                cur_appearance_loss_avg = 0.0

                if (prev_z is None):
                    prev_z = z
                else:
                    for j in range(texture_video_length):
                        '''Compute Texture loss between current generated image and all texture frames'''
                        target_images = target_image_seq_texture[j:j + 1]
                        generated_images = z
                        b, c, h, w = generated_images.shape
                        _, _, ht, wt = target_images.shape
                        generated_images = (generated_images + 1.0) / 2.0
                        target_images = (target_images + 1.0) / 2.0

                        if h != ht or w != wt:
                            target_images = TF.resize(target_images, size=(h, w))
                        appearance_loss = loss_class[0](target_images, generated_images)[0]
                        cur_appearance_loss_avg += appearance_loss.item()
                    cur_appearance_loss_avg /= texture_video_length

                    for j in range(motion_video_length - 1):
                        target_motion_image_list = []
                        target_motion_image_list.append(target_image_seq[j:j + 1])
                        target_motion_image_list.append(target_image_seq[j + 1:j + 2])
                        video_motion_loss, _ = loss_class[1].forward(generated_image_list=[prev_z, z], 
                                                target_image_list=target_motion_image_list, return_summary=False)
                        cur_video_motion_loss_avg += video_motion_loss.item()
                    cur_video_motion_loss_avg /= (motion_video_length - 1)

                    total_appearance_loss_avg += cur_appearance_loss_avg
                    total_video_motion_loss_avg += cur_video_motion_loss_avg

                    prev_z = z

            h = nca_state

            # img = z.detach().cpu().numpy()[0]
            # img = img.transpose(1, 2, 0)
            img = z.permute([0, 2, 3, 1]).detach().cpu().numpy()
            img = ((np.clip(img, -1, 1)+1)/2.0 * 255.0).astype(np.uint8).squeeze()
            # img = np.clip(img, -1.0, 1.0)
            # img = (img + 1.0) / 2.0
            vid.add(img)
        if (record_loss):
            total_appearance_loss_avg /= float(video_length * 40)
            total_video_motion_loss_avg /= float(video_length * 40)
            with open(f'{output_dir}/final_loss_test.txt', 'w') as f:
                f.write(f'total_appearance_loss_avg: {total_appearance_loss_avg}, total_video_motion_loss_avg: {total_video_motion_loss_avg}')